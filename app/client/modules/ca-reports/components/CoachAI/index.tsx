// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import React, { createContext, useContext, useState } from 'react';
import { QueryClientProvider } from '@cultureamp/frontend-apis';
import { Drawer } from '@cultureamp/reports-components';

import { Button, Icon } from '@kaizen/components/next';
import { Text } from '@kaizen/components';
import useFeatureFlag from 'ca-reports/hooks/useFeatureFlag';
import {
  CoachHeader,
  CoachChat,
  CoachContextProvider,
} from '@cultureamp/ai-shared-ui';

export const CoachDrawerContext = createContext<{
  isDrawerOpened: boolean;
  toggleDrawer: () => void;
}>({
  isDrawerOpened: false,
  toggleDrawer: () => {},
});

export const CoachDrawerContextProvider = ({
  children,
}: {
  isDrawerOpened: boolean;
  children: React.ReactNode;
}) => {
  const { isDrawerOpened } = useContext(CoachDrawerContext);
  const [isDrawerOpenedState, setIsDrawerOpenedState] =
    useState(isDrawerOpened);

  return (
    <CoachDrawerContext.Provider
      value={{
        isDrawerOpened: isDrawerOpenedState,
        toggleDrawer: () => setIsDrawerOpenedState(!isDrawerOpenedState),
      }}
    >
      {children}
    </CoachDrawerContext.Provider>
  );
};

const CoachFeature = ({ children }) => {
  const isCoachEnabled = useFeatureFlag('engage_coach_enabled');

  if (!isCoachEnabled) {
    return null;
  }

  return <CoachDrawerContextProvider>{children}</CoachDrawerContextProvider>;
};

export const ChatToCoachButton = () => {
  const { toggleDrawer } = useContext(CoachDrawerContext);

  return (
    <CoachFeature>
      <div className="fixed bottom-128 right-32 z-10 flex flex-col items-end">
        <div className="bg-gradient-to-tl from-[#C888E2] via-[#64D2D7] to-[#67C7AF] rounded-[100px] p-2 mb-12 shadow-[0px_3px_9px_0px_rgba(0,0,0,0.10)]">
          <div className="py-12 px-24 text-center bg-white rounded-[100px]">
            <Text classNameOverride="font-weight-paragraph-bold">
              <Icon
                name="flag"
                isPresentational
                className="pr-12 text-[#3F9A86]"
              />
              Need help thinking about what to do now?
            </Text>
          </div>
        </div>
        <div>
          <Button
            variant="primary"
            onPress={toggleDrawer}
            size="large"
            className={
              'py-12 px-24 text-center shadow-[0px_3px_9px_0px_rgba(0,0,0,0.10)] bg-gradient-to-r from-[#844587] from-50% hover:from-[#5F3361] to-[#73C0E8] hover:to-[#73C0E8] rounded-[100px] font-weight-paragraph-bold border-2 outline-[#67C7AF] border-[#67C7AF] outline-4'
            }
          >
            <Icon
              name="auto_awesome"
              isPresentational
              className="pr-12 text-white"
            />
            Ask Coach
          </Button>
        </div>
      </div>
    </CoachFeature>
  );
};

const CoachAIDrawer = ({
  resource,
  resourceId,
  contextUrl,
  coachParams,
}: {
  resource: string;
  resourceId: string;
  contextUrl: string;
  coachParams: {
    reportId: string;
    surveyId: string;
    selectedLeader: string | null;
    anchorFilters: string | null;
    demographicFilters: string | null;
    accountContext: object;
    userContext: object;
  };
}) => {
  const { isDrawerOpened, toggleDrawer } = useContext(CoachDrawerContext);
  const isMcpEnabled = useFeatureFlag('reporting_mcp_server_enabled');
  let coachContextProviderProps;
  let coachChatProps;
  if (isMcpEnabled) {
    const queryString = window.location.search;
    const params = new URLSearchParams(queryString);

    coachContextProviderProps = {
      resource,
      context: coachParams,
    };
    coachChatProps = {
      isDrawer: true,
      endpoint: '/api/ai-coach/v2/action_planning',
    };
  } else {
    coachContextProviderProps = {
      resource,
      resourceId,
      contextUrl,
    };
    coachChatProps = {
      isDrawer: true,
    };
  }

  return (
    <CoachFeature>
      <QueryClientProvider>
        <CoachContextProvider {...coachContextProviderProps}>
          <Drawer isOpen={isDrawerOpened} onClose={toggleDrawer}>
            {isDrawerOpened && (
              <div className="reset-bootstrap">
                <CoachHeader />
                <CoachChat {...coachChatProps} />
              </div>
            )}
          </Drawer>
        </CoachContextProvider>
      </QueryClientProvider>
    </CoachFeature>
  );
};

export default CoachAIDrawer;
