class FiltersController < ReportsController
  component :report

  REPORT_TYPE = :filters
  allows_jwt_authentication

  expose(:participation_result) do
    if params[:demographic]
      report_data_service.without_hierarchy_privacy.participation_result_for_demographic(params[:demographic]).tap do |result|
        response.etag = nil if result.error?
      end
    else
      report_data_service.without_hierarchy_privacy.participation_result.tap do |result|
        response.etag = nil if result.error?
      end
    end
  end

  def data
    unless report_sharing?
      admin = @context.user.has_full_access?(@context.survey, @scope)
      fail "Configuration error - user #{@context.user.email} doesn't have a filter anchor" if !admin && @context.filter_anchor.nil?
    end

    render json: JSON(filter_data)
  end

  def process_filters(context = @context)
    process_filter_result = super(context) # calls Filterable.process_filters
    return process_filter_result unless params[:demographic]

    context.filters = partitioned_report_filters.without_selected_demographic
  end

  protected

  def filter_data
    return {} if participation_result.error?

    Props::DemographicFilterProps.new(context, @scope, participation_result.data, report_sharing?, reporting_rules, demographic_stq_id: params[:demographic], options_to_display: options_to_display).to_hash
  end

  def establish_legacy_report_scope
    filters_scope
  end

  private

  def reporting_rules
    Reporting::Confidentiality::StandardRules.new.call(survey: survey)
  end

  def partitioned_report_filters
    @partitioned_report_filters ||= ReportFilters::PartitionByDemographic.new(params[:demographic], context.filters)
  end

  def options_to_display
    anchor_filters = determine_anchor_filters

    total_participation_result = report_data_service
      .without_hierarchy_privacy
      .total_participation_result_for_demographic(params[:demographic], anchor_filters)

    return [] if total_participation_result.error?

    total_participation_result
      .data
      .segment_results_for_first_segment_class_result
      .reject { |segment_result| segment_result.submission_count.zero? }
      .map(&:id)
  end

  def determine_anchor_filters
    # TODO: This can be removed once single anchors are fully removed
    has_multi_demographic_access = has_access_to_view_multi_demographic_reports?(
      account_id: @context.account.aggregate_id,
      user_id: @context.user.aggregate_id,
      survey_id: @context.survey.aggregate_id
    )

    if has_multi_demographic_access
      determine_anchors
    else
      determine_legacy_anchor
    end
  end

  def determine_anchors
    if context.anchors.any? { |a| a&.hierarchy? } || full_reporting_line?
      {hierarchy_filters: [context.anchors.first.to_param]}
    elsif context.anchors.present? && !context.anchors.first.is_a?(AllResultsReportFilter)
      {filters: context.anchors.map(&:to_param)}
    end
  end

  def determine_legacy_anchor
    if context.anchor&.hierarchy? || full_reporting_line?
      {hierarchy_filters: [context.anchor.to_param]}
    elsif context.anchor.present? && !context.anchor.is_a?(AllResultsReportFilter)
      {filters: [context.anchor.to_param]}
    end
  end
end
