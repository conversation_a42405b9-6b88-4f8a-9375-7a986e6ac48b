module CommunicationsApi
  class MetadataController < ApplicationController
    include SurveyConfigurationSideNav
    include SurveyConfigurationTitleblock
    include VerifySameOrigin
    replace_csrf_with_origin_check
    allows_access_to :all_authenticated_users

    before_action :check_permission
    def sidebar_navigation_menu
      render json: {updatedSidebar: true, surveyAggregateId: survey.aggregate_id, accountId: survey.account.id.to_s}
    end

    def title_block
      render json: construct_titleblock_props
    end

    def admin_emails
      account = survey.account
      account_admin_ids = account.active_account_admin_grants.pluck(:person_id)
      survey_admins_ids = survey.survey_admin_grants.active.pluck(:person_id)
      surveys_admins_ids = Roles::Queries::FindUsersByRole.new.call(account_id: account.aggregate_id.to_s, role_key: :surveys_admin).map(&:id)
      all_admins = account.people.active.in(id: account_admin_ids + survey_admins_ids + surveys_admins_ids).where(:email.nin => ["", nil])
      data = all_admins.map { |p|
        {
          name: p.display_name,
          email: p.email
        }
      }
      render json: data
    end

    def supported_locales
      result = survey.supported_locales.map { |locale|
        make_locale_option(locale)
      }
      render json: result
    end

    private

    def check_permission
      render_forbidden unless
          Authorization.permitted?(
            user_id: current_user.aggregate_id,
            resource_id: survey.account&.aggregate_id,
            permission: Account::Permissions::ADMINISTER_SURVEY
          ) || Authorization.permitted?(
            user_id: current_user.aggregate_id,
            resource_id: survey.aggregate_id,
            permission: Account::Permissions::ADMINISTER_SURVEY
          )
    end

    def survey
      Survey.find(params[:survey_id])
    end

    def make_locale_option(locale)
      {
        code: locale.to_s,
        label: I18n.translate("i18n.locale.name.#{locale}", locale: :en)
      }
    end
  end
end
