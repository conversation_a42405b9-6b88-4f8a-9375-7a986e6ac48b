module CommunicationsApi
  class EmailTemplatesController < ApplicationController
    include VerifySameOrigin
    include ActionView::Helpers::SanitizeHelper

    replace_csrf_with_origin_check
    allows_access_to :all_authenticated_users

    before_action :check_permission

    def raw_content
      locale = params.fetch(:locale, survey.default_locale)
      template_type = params[:email_template_id]

      result, content = Communications::Email::CommunicationTemplateData.new.call(
        survey: survey,
        demo_response: demo_response,
        locale: locale,
        template_type: template_type
      )

      if result == :ok
        render status: :ok, json: content
      else
        render status: :not_found, json: {error: content}
      end
    end

    def show
      locale = sanitize(params.fetch(:locale, survey.default_locale))
      template_type = sanitize(params[:id])
      find_communication_template = Communications::Email::FindCommunicationTemplate.new
      template = find_communication_template.call(
        survey_id: survey.id,
        template_type: template_type
      )

      result, template_content = Communications::Email::RenderedTemplateContent.new.call(
        survey: survey,
        response: demo_response,
        locale: locale,
        template: template
      )

      if result == :ok
        render html: template_content["message"].html_safe
      else
        render_not_found
      end
    end

    def subject
      locale = params.fetch(:locale, survey.default_locale)
      template_type = params[:email_template_id]
      find_communication_template = Communications::Email::FindCommunicationTemplate.new
      template = find_communication_template.call(
        survey_id: survey.id,
        template_type: template_type
      )

      result, template_content = Communications::Email::RenderedTemplateContent.new.call(
        survey: survey,
        response: demo_response,
        locale: locale,
        template: template
      )

      if result == :ok
        render status: :ok, json: {subject: template_content["subject"]}
      else
        render status: :not_found, json: {}
      end
    end

    def update
      locale = params.fetch(:locale, survey.default_locale)
      updated_content = params.to_unsafe_h.fetch("content", [])
      updated_subject = params.to_unsafe_h.fetch("subject", [])
      template_type = params[:id]

      result, errors = Communications::Email::UpdateTemplateContent.new.call(
        survey_id: survey.id,
        content: updated_content,
        subject: updated_subject,
        locale: locale,
        template_type: template_type
      )

      if result == :ok
        render status: :ok, json: {}
      elsif result == :missing_template
        render status: :not_found, json: {}
      else
        render status: :unprocessable_entity, json: {errors: errors}
      end
    end

    def send_test
      locale = params.fetch(:locale, survey.default_locale)
      template_type = params[:email_template_id]
      users = User.in(email: params[:user_emails]).where(account_id: survey.account.id)
      recipients = users & admin_users
      recipients = [current_user] if recipients.empty?

      recipients.each do |recipient|
        result, error_msg = Communications::Email::SendEmail.new.call(
          account: survey.account,
          survey: survey,
          response: demo_response(name: recipient.display_name),
          recipient: recipient,
          template_type: template_type,
          locale: locale
        )

        render status: :not_found, json: {error: error_msg} if result == :error
      end

      render status: :ok, json: {}
    end

    private

    def check_permission
      render_forbidden unless
          Authorization.permitted?(
            user_id: current_user.aggregate_id,
            resource_id: survey.account.aggregate_id,
            permission: Account::Permissions::ADMINISTER_SURVEY
          ) || Authorization.permitted?(
            user_id: current_user.aggregate_id,
            resource_id: survey.aggregate_id,
            permission: Account::Permissions::ADMINISTER_SURVEY
          )
    end

    def demo_response(name: current_user.display_name)
      CreateDemonstrationResponse.new(
        survey: survey,
        type: survey.type.to_s,
        demographics: [],
        name: name
      ).call
    end

    def survey
      Survey.find(params[:survey_id])
    end

    def admin_users
      account = survey.account
      account_admin_ids = account.active_account_admin_grants.pluck(:person_id)
      survey_admins_ids = survey.survey_admin_grants.active.pluck(:person_id)
      surveys_admins_ids = Roles::Queries::FindUsersByRole.new.call(account_id: account.aggregate_id.to_s, role_key: :surveys_admin).map(&:id)
      account.people.active.in(id: account_admin_ids + survey_admins_ids + surveys_admins_ids).where(:email.nin => ["", nil])
    end
  end
end
