module Home
  class AvailableReportsController < Home::BaseController
    include VerifySameOrigin
    include ReportSharing::Queries::MultiDemographicFeatureFlag
    replace_csrf_with_origin_check
    allows_jwt_authentication
    def index
      show_survey_reporting_ui_report_index = FeatureFlags::Queries::ValueForContext.new.call(
        user_id: current_user&.aggregate_id,
        account_id: current_user.account&.aggregate_id,
        flag_name: FeatureFlags::Flags::SURVEY_REPORTING_UI_REPORT_INDEX_ENABLE,
        fallback_value: false
      )

      raise ActionController::RoutingError.new("Not Found"), "Not Found" unless show_survey_reporting_ui_report_index

      show_multidemographic_reports = has_customer_access_to_multi_demographic_reports?(
        user_id: current_user&.aggregate_id,
        account_id: current_user.account&.aggregate_id,
        survey_id: nil
      )

      user_reports = show_multidemographic_reports ? EmployeeHomepage::UserMultidemographicReportsAggregation.new(
        user: current_user,
        account: account
      ) : EmployeeHomepage::UserReportsAggregation.new(
        user: current_user,
        account: account
      )

      render json: user_reports.all_reports
    end

    def handle_unauthenticated
      render json: error_response("Unauthorized"), status: :unauthorized
    end

    def error_response(code)
      {errors: [{code: code}]}
    end
  end
end
