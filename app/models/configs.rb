# Configs used by the configuration system
# Do not use symbols for items.
module Configs
  # Available values for various options
  VALUES = {}
  DESCRIPTIONS = {}

  # Some useful defaults
  ENABLED = "enabled"
  DISABLED = "disabled"
  EXTERNAL = "external"

  FIELD_NAME = :configuration

  Boolean = Mongoid::Boolean

  ALLOWED_CLOCK_DRIFT_IN_SECONDS = "allowed_clock_drift_in_seconds"
  VALUES[ALLOWED_CLOCK_DRIFT_IN_SECONDS] = Integer
  DESCRIPTIONS[ALLOWED_CLOCK_DRIFT_IN_SECONDS] = "The difference in seconds that we allow between servers for SAML timestamps"

  ALLOW_GROUP_PARTICIPANT_LIMIT = 5_000
  ALLOW_GROUP_PARTICIPANTS = "allow_participant_grouping"
  VALUES[ALLOW_GROUP_PARTICIPANTS] = [nil, DISABLED, ENABLED]
  DESCRIPTIONS[ALLOW_GROUP_PARTICIPANTS] = 'For large accounts. If disabled, prevents the "group" option from showing up on the Participant page in survey designer. '

  CAPTURE_LIKERT_DEFAULT_COLOR_SCHEMA = "classical"
  CAPTURE_LIKERT_COLOR_SCHEMA = "capture_likert_color_schema"
  VALUES[CAPTURE_LIKERT_COLOR_SCHEMA] = ["classical", "blue"]
  DESCRIPTIONS[CAPTURE_LIKERT_COLOR_SCHEMA] = "Changes the color schema for the likert scale (default is the classical yellow-to-red)"

  CAPTURE_DEFAULT_RADIO_THRESHOLD = 12
  CAPTURE_RADIO_THRESHOLD = "capture_radio_threshold"
  VALUES[CAPTURE_RADIO_THRESHOLD] = Integer
  DESCRIPTIONS[CAPTURE_RADIO_THRESHOLD] = "Changes the threshold that stops rendering radio buttons and instead renders a select box for single select questions"

  FREE_SURVEY_RESPONSE_LIMIT_DEFAULT = 2_000
  FREE_SURVEY_RESPONSE_LIMIT = "free_survey_response_limit"
  VALUES[FREE_SURVEY_RESPONSE_LIMIT] = Integer
  DESCRIPTIONS[FREE_SURVEY_RESPONSE_LIMIT] = "For free accounts. Set the maximum allowed number of responses per survey. The default is #{FREE_SURVEY_RESPONSE_LIMIT_DEFAULT}."

  ANONYMOUS_AUTHORIZATION_CODE = "anonymous_authorization_code"
  VALUES[ANONYMOUS_AUTHORIZATION_CODE] = String
  DESCRIPTIONS[ANONYMOUS_AUTHORIZATION_CODE] = "Code that must be provided by survey participants taking an Anonymous Adhoc survey - see ANONYMOUS"

  FLEXIBLE_COMMENTS_FILTER = "flexible_comments_filter"
  VALUES[FLEXIBLE_COMMENTS_FILTER] = :demographic_codes
  DESCRIPTIONS[FLEXIBLE_COMMENTS_FILTER] = "Confidentiality risk: use with *extreme caution* to override single comment filter with the provided demographic codes."

  DATE_FORMAT = "date_format"
  VALUES[DATE_FORMAT] = String
  DESCRIPTIONS[DATE_FORMAT] = 'The default date format used e.g. "%m/%d/%Y", http://www.ruby-doc.org/core-2.0/Time.html#method-i-strftime'

  DAYS_ELIGABLE_AFTER_EXIT = "days_eligable_after_exit"
  DESCRIPTIONS[DAYS_ELIGABLE_AFTER_EXIT] = "The number of days after someone is exited that they can be sent an exit survey. (Defaults to zero)"
  VALUES[DAYS_ELIGABLE_AFTER_EXIT] = Integer

  DEMONSTRABLE = "demonstrable"
  DESCRIPTIONS[DEMONSTRABLE] = "Allow this account to be used as demo account"
  VALUES[DEMONSTRABLE] = Boolean

  DEMO_MODE = "demo_mode"
  DESCRIPTIONS[DEMO_MODE] = "Provides demo help as the user walks through the survey"
  VALUES[DEMO_MODE] = Boolean

  DEMOGRAPHIC_PRELOAD_LIMIT = "demographic_preload_limit"
  DESCRIPTIONS[DEMOGRAPHIC_PRELOAD_LIMIT] = "Limits the initial amount of demographic values loaded"
  VALUES[DEMOGRAPHIC_PRELOAD_LIMIT] = Integer

  DRIVER_DEMOGRAPHIC = "driver_demographic"
  VALUES[DRIVER_DEMOGRAPHIC] = :demographic_codes
  DESCRIPTIONS[DRIVER_DEMOGRAPHIC] = "Calculates the driver analysis for the user's segment of this demographic. For example, if the demographic was Department, employees of the Engineering department would see the drivers for Engineering."

  EMPLOYEE_HIERARCHY_QUESTION = "employee_hierarchy_question"
  VALUES[EMPLOYEE_HIERARCHY_QUESTION] = :question_codes
  DESCRIPTIONS[EMPLOYEE_HIERARCHY_QUESTION] = "The question code that represents the manager hieararchy question in a survey"

  ENFORCES_AUTH_FOR_CAPTURE = "enforces_auth_for_capture"
  VALUES[ENFORCES_AUTH_FOR_CAPTURE] = Boolean
  DESCRIPTIONS[ENFORCES_AUTH_FOR_CAPTURE] = "If enabled, users will be required to authenticate before responding to a survey."

  # FIXME: insights https://cultureamp.atlassian.net/browse/INX-462
  EXCLUDED_DRIVER_OPTION = "excluded_driver_option"
  VALUES[EXCLUDED_DRIVER_OPTION] = String
  DESCRIPTIONS[EXCLUDED_DRIVER_OPTION] = "The select option value of the demographic that should see driver analysis at the company level, not by their demographic. For example, this might be 'Management'. (This option is only meaningful if 'driver_demographic' is also set.)"

  EXPERIMENT_A_AND_U_DEV_ENGAGEMENT_TREND = "experiment_a_and_u_dev_engagement_trend"
  VALUES[EXPERIMENT_A_AND_U_DEV_ENGAGEMENT_TREND] = "A json array of objects containing the following keys: question_ids, survey_id, name, created_on, closed_on and favorability_scores"
  DESCRIPTIONS[EXPERIMENT_A_AND_U_DEV_ENGAGEMENT_TREND] = "This config needs to be used only for demo purposes. If you want to restrict the survey data points appearing on the trend line, use Report Configurations"

  HIDE_SECRET_URL = "hide_secret_url"
  VALUES[HIDE_SECRET_URL] = [DISABLED, ENABLED]
  DESCRIPTIONS[HIDE_SECRET_URL] = "Secret token are stored in cookies and hidden from the URL. Enabled by default."

  EFFECTIVENESS_360_EMPLOYEE_DRIVEN = "effectiveness_360_employee_driven"
  VALUES[EFFECTIVENESS_360_EMPLOYEE_DRIVEN] = [DISABLED, ENABLED]
  DESCRIPTIONS[EFFECTIVENESS_360_EMPLOYEE_DRIVEN] = "Configures whether an effectiveness 360 survey is employee driven(enabled) or admin-driven(disabled)"

  EFFECTIVENESS_360_COACHED = "effectiveness_360_coached"
  VALUES[EFFECTIVENESS_360_COACHED] = [DISABLED, ENABLED]
  DESCRIPTIONS[EFFECTIVENESS_360_COACHED] = "Configures whether an effectiveness 360 survey is coached(enabled) or coachless(disabled)"

  KIOSK_CODE = "kiosk_code"
  GENERATE_KIOSK_CODE = "generate"
  EMPLOYEE_ID_KIOSK_CODE = "employee_id"
  DESCRIPTIONS[KIOSK_CODE] = "Specify whether we should generate kiosk codes, or if participants should use their employee ids"
  VALUES[KIOSK_CODE] = [GENERATE_KIOSK_CODE, EMPLOYEE_ID_KIOSK_CODE]

  LIFECYCLE_WORKFLOW = "lifecycle_workflow"
  DESCRIPTIONS[LIFECYCLE_WORKFLOW] = "Configures whether a lifecycle survey uses the 'full' workflow or not. A 'full' workflow includes a step where a different employee (called an interviewer) fills in some extra questions. Only applies to exit surveys; all onboarding surveys use a basic workflow"
  LIFECYCLE_WORKFLOW_FULL = "full"
  LIFECYCLE_WORKFLOW_BASIC = "basic"
  VALUES[LIFECYCLE_WORKFLOW] = [LIFECYCLE_WORKFLOW_FULL, LIFECYCLE_WORKFLOW_BASIC]

  EXPERIMENT_MANAGER_DASHBOARD_USERS = "manager_dashboard_users"
  VALUES[EXPERIMENT_MANAGER_DASHBOARD_USERS] = Array
  DESCRIPTIONS[EXPERIMENT_MANAGER_DASHBOARD_USERS] = "Account user aggregate ids that are allowed to see the manager dashboard"

  EXPERIMENT_MANAGER_DASHBOARD_HIERARCHY_QUESTION_CODE = "experiment_manager_dashboard_hierarchy_question_code"
  VALUES[EXPERIMENT_MANAGER_DASHBOARD_HIERARCHY_QUESTION_CODE] = String
  DESCRIPTIONS[EXPERIMENT_MANAGER_DASHBOARD_USERS] = "Question code of hierarchy to use for the manager dashboard"

  MAX_RAW_DATA_EXTRACT_SIZE = "max_raw_data_extract_size"
  VALUES[MAX_RAW_DATA_EXTRACT_SIZE] = Integer
  DESCRIPTIONS[MAX_RAW_DATA_EXTRACT_SIZE] = "Maximum numbers of response allowed for survey raw data to be exported"

  MAXIMUM_REPORT_FILTERS = "maximum_report_filters"
  VALUES[MAXIMUM_REPORT_FILTERS] = Integer
  DESCRIPTIONS[MAXIMUM_REPORT_FILTERS] = "Maximum number of filters that will be available to report users"

  MURMUR_FEEDBACK_SCREEN = "murmur_feedback_screen"
  VALUES[MURMUR_FEEDBACK_SCREEN] = [DISABLED, ENABLED]
  DESCRIPTIONS[MURMUR_FEEDBACK_SCREEN] = "Determines if the Murmur Feedback screen is presented upon submission of a survey response"

  OVERALL_LABEL = "overall_label"
  VALUES[OVERALL_LABEL] = String
  DESCRIPTIONS[OVERALL_LABEL] = "Specifies that name of the organization's overall label on the manager insight report"

  PDF_PAGE_SIZE = "pdf_page_size"
  VALUES[PDF_PAGE_SIZE] = %w[A4 Letter]
  DESCRIPTIONS[PDF_PAGE_SIZE] = "Default page size for PDFs"

  PREVIEW_DEMO_MODE = "preview_demo_mode"
  VALUES[PREVIEW_DEMO_MODE] = Boolean
  DESCRIPTIONS[PREVIEW_DEMO_MODE] = "If enabled, all reports use preview mode, without the banner. Used for demonstration surveys and accounts."

  RANDOM_DEMOGRAPHIC_CODE = "random_demographic_code"
  DESCRIPTIONS[RANDOM_DEMOGRAPHIC_CODE] = "When present a random demographic value for this demographic will be assigned to participants in the trigger invite process of this survey"
  VALUES[RANDOM_DEMOGRAPHIC_CODE] = :question_codes

  RAW_DATA_EXTRACT = "raw_data_extract"
  VALUES[RAW_DATA_EXTRACT] = [DISABLED, ENABLED]
  DESCRIPTIONS[RAW_DATA_EXTRACT] = "Allow raw data extract of survey response data"

  REPORT_HIERARCHY_RECURSION = "report_hierarchy_recursion"
  VALUES[REPORT_HIERARCHY_RECURSION] = Integer
  DESCRIPTIONS[REPORT_HIERARCHY_RECURSION] = "Determines how many levels the Direct Reports for a manager should be included - this will be rarely (if ever) used."

  REPORT_SHARING_ADD_ALL_USERS_LIMIT = "report_sharing_add_all_users_limit"
  VALUES[REPORT_SHARING_ADD_ALL_USERS_LIMIT] = Integer
  DESCRIPTIONS[REPORT_SHARING_ADD_ALL_USERS_LIMIT] = "Accounts with more users than this limit will not see the 'Add All' option in Report Sharing (this option is deprecated)"

  REPORT_SHARING_ADD_PARTICIPANTS_LIMIT = "report_sharing_add_participants_limit"
  VALUES[REPORT_SHARING_ADD_PARTICIPANTS_LIMIT] = Integer
  DESCRIPTIONS[REPORT_SHARING_ADD_PARTICIPANTS_LIMIT] = "Reports with more participants than this limit will not see the 'Add Participants' option in Report Sharing."

  REPORT_SHARING_MULTI_DEMOGRAPHICS_MAX_REPORT_COMBINATIONS = "report_sharing_multi_demographics_max_report_combinations"
  VALUES[REPORT_SHARING_MULTI_DEMOGRAPHICS_MAX_REPORT_COMBINATIONS] = Integer
  DESCRIPTIONS[REPORT_SHARING_MULTI_DEMOGRAPHICS_MAX_REPORT_COMBINATIONS] = "The maximum number of report combinations that can be generated for multi-demographic reports"

  SIGNIFICANCE_COMMENT_POPULATION = "significance_comment_population"
  VALUES[SIGNIFICANCE_COMMENT_POPULATION] = Integer
  DESCRIPTIONS[SIGNIFICANCE_COMMENT_POPULATION] = "Minimum number of submitted responses within a group required to display comments results in a report"

  SIGNIFICANCE_DRIVER_ANALYSIS = "significance_driver_analysis"
  VALUES[SIGNIFICANCE_DRIVER_ANALYSIS] = Integer
  DESCRIPTIONS[SIGNIFICANCE_DRIVER_ANALYSIS] = "Minimum number of submitted responses required for driver analysis to run"

  SIGNIFICANCE_POPULATION = "significance_population"
  VALUES[SIGNIFICANCE_POPULATION] = Integer
  DESCRIPTIONS[SIGNIFICANCE_POPULATION] = "Minimum number of submitted responses within a group required to display results in a report"

  SIGNIFICANCE_LIFECYCLE_POPULATION = "significance_lifecycle_population"
  VALUES[SIGNIFICANCE_LIFECYCLE_POPULATION] = Integer
  DESCRIPTIONS[SIGNIFICANCE_LIFECYCLE_POPULATION] = "Minimum number of submitted responses within a group required to display results in any lifecycle survey reports. Note, for individual responses to be viewable in the activity report, the value must be zero (which is also the 'default' value for this configuration item)."

  INDIRECT_IDENTIFICATION_PROTECTION_LEVEL = "indirect_identification_protection_level"
  VALUES[INDIRECT_IDENTIFICATION_PROTECTION_LEVEL] = Reporting::Confidentiality::IndirectIdentificationProtectionLevel::AVAILABLE_LEVELS
  DESCRIPTIONS[INDIRECT_IDENTIFICATION_PROTECTION_LEVEL] = "The level of indirect identification protection to be used if the survey is NOT a lifecycle survey. 'off' -> No indirect identification protections. 'individuals' -> Only demographic groups with one member will be protected. 'minimum_reporting_group' -> Groups smaller than minimum_reporting_group (aka significance) will be protected."

  LIFECYCLE_INDIRECT_IDENTIFICATION_PROTECTION_LEVEL = "lifecycle_indirect_identification_protection_level"
  VALUES[LIFECYCLE_INDIRECT_IDENTIFICATION_PROTECTION_LEVEL] = Reporting::Confidentiality::IndirectIdentificationProtectionLevel::AVAILABLE_LEVELS
  DESCRIPTIONS[LIFECYCLE_INDIRECT_IDENTIFICATION_PROTECTION_LEVEL] = "The level of indirect identification protection to be used if the survey is a lifecycle survey. 'off' -> No indirect identification protections. 'individuals' -> Only demographic groups with one member will be protected. 'minimum_reporting_group' -> Groups smaller than minimum_reporting_group (aka significance) will be protected."

  ALLOW_SLACK_IN_NON_PRODUCTION = "allow_slack_in_non_production"
  VALUES[ALLOW_SLACK_IN_NON_PRODUCTION] = [DISABLED, ENABLED]
  DESCRIPTIONS[ALLOW_SLACK_IN_NON_PRODUCTION] = "Allow Slack notifications to be sent in environments other than production (Account Level Only)"

  SUPPORT_CONTACT = "support_contact"
  DESCRIPTIONS[SUPPORT_CONTACT] = "The name of the person/team participants should contact if they require help. (Only displayed if a contact email is also specified)."
  VALUES[SUPPORT_CONTACT] = String

  SUPPORT_EMAIL = "support_email"
  DESCRIPTIONS[SUPPORT_EMAIL] = "The email address participants should contact if they require help."
  VALUES[SUPPORT_EMAIL] = String

  SEND_ENGAGEMENT_EMAILS = "send_engagement_emails"
  DESCRIPTIONS[SEND_ENGAGEMENT_EMAILS] = "Send engagement emails"
  VALUES[SEND_ENGAGEMENT_EMAILS] = [DISABLED, ENABLED]

  SEND_THREE_SIXTY_EMAILS = "send_three_sixty_emails"
  DESCRIPTIONS[SEND_THREE_SIXTY_EMAILS] = "Send emails for three sixty surveys"
  VALUES[SEND_THREE_SIXTY_EMAILS] = [DISABLED, ENABLED]

  SUPPORTED_LOCALES = "supported_locales"
  VALUES[SUPPORTED_LOCALES] = :supported_locales
  DESCRIPTIONS[SUPPORTED_LOCALES] = "An array of language_region codes supported by the survey. Account level locales dont make much sense as the content of the survey dictates what locales are feasible"

  SUPPORTED_PRODUCTS = "supported_products"
  VALUES[SUPPORTED_PRODUCTS] = :supported_products
  DESCRIPTIONS[SUPPORTED_PRODUCTS] = "An array of the supported products"

  SURVEY_CONFIRMATION_SCREEN = "survey_confirmation_screen"
  SURVEY_CONFIRMATION_SCREEN_DISABLED = "disabled"
  SURVEY_CONFIRMATION_SCREEN_ENABLED = "enabled"
  VALUES[SURVEY_CONFIRMATION_SCREEN] = [SURVEY_CONFIRMATION_SCREEN_DISABLED, SURVEY_CONFIRMATION_SCREEN_ENABLED]
  DESCRIPTIONS[SURVEY_CONFIRMATION_SCREEN] = "Specifies if the confirmation screen should be shown - note, only relevant for Adobe"

  SURVEY_INVITE_BATCH_SIZE = "survey_invite_batch_size"
  VALUES[SURVEY_INVITE_BATCH_SIZE] = Integer
  DESCRIPTIONS[SURVEY_INVITE_BATCH_SIZE] = "Specifies the batch size of survey invite jobs"

  SURVEY_QUESTION_CLASSIFICATION = "survey_question_classification"
  VALUES[SURVEY_QUESTION_CLASSIFICATION] = String
  DESCRIPTIONS[SURVEY_QUESTION_CLASSIFICATION] = "Specifies the classification scheme used for score colourings (factor and question) on surveys. Currently the only option is 'adobe' or nil (default)"

  TRIGGER_ELIGIBILITY_FLAG = "trigger_eligibility_flag"
  DESCRIPTIONS[TRIGGER_ELIGIBILITY_FLAG] = "If set, only users with this demographic flag set to true in their master record will be included eligible for lifecycle triggering"
  VALUES[TRIGGER_ELIGIBILITY_FLAG] = :demographic_codes

  CAN_PICK_ANY_DATE = "can_pick_any_date"
  VALUES[CAN_PICK_ANY_DATE] = Boolean
  DESCRIPTIONS[CAN_PICK_ANY_DATE] = "True if any date can be selected from date picker. False if the date range is to be restricted."

  RUN_REPORT_DATA_SERVICE_EXPERIMENT = "run_report_data_service_experiment"
  VALUES[RUN_REPORT_DATA_SERVICE_EXPERIMENT] = Boolean
  DESCRIPTIONS[RUN_REPORT_DATA_SERVICE_EXPERIMENT] = "Run a comparison experiment between the reference and experimental services when requesting reporting data"

  THROTTLE_SURVEY_INVITATIONS_PER_SECOND = "throttle_survey_invitations_per_second"
  DESCRIPTIONS[THROTTLE_SURVEY_INVITATIONS_PER_SECOND] = "Max number of survey invitations to queue up per second. 0 - no limit"
  VALUES[THROTTLE_SURVEY_INVITATIONS_PER_SECOND] = Integer

  MAXIMUM_REPORT_DEMOGRAPHIC_SPREAD_SIZE = "maximum_report_demographic_spread_size"
  DESCRIPTIONS[MAXIMUM_REPORT_DEMOGRAPHIC_SPREAD_SIZE] = "Max number of demographic values a demographic has to show its spread on the insight reports"
  VALUES[MAXIMUM_REPORT_DEMOGRAPHIC_SPREAD_SIZE] = Integer

  SHOW_THREE_SIXTY_REVIEWER_NAMES = "show_three_sixty_reviewer_names"
  VALUES[SHOW_THREE_SIXTY_REVIEWER_NAMES] = [DISABLED, ENABLED]
  DESCRIPTIONS[SHOW_THREE_SIXTY_REVIEWER_NAMES] = "Reviewer names show on Individual Effectiveness (360) Feedback Reviews"

  USER_FOR_360_CAPTURE_PREVIEW = "user_for_360_capture_preview"
  VALUES[USER_FOR_360_CAPTURE_PREVIEW] = String
  DESCRIPTIONS[USER_FOR_360_CAPTURE_PREVIEW] = "Email address - when previewing capture for a 360 survey, this user will be the one whose review is being completed"

  ENPS_QUESTION_CODE = "enps_question_code"
  VALUES[ENPS_QUESTION_CODE] = :question_codes
  DESCRIPTIONS[ENPS_QUESTION_CODE] = 'Question code of the "I would recommend..." question to display Employee NPS'

  QUESTIONS_TO_EXCLUDE_FROM_DRIVER_ANALYSIS = "questions_to_exclude_from_driver_analysis"
  DESCRIPTIONS[QUESTIONS_TO_EXCLUDE_FROM_DRIVER_ANALYSIS] = 'Question codes of questions to exclude from driver analysis (e.g. ["hooli.murmur50.ENG.32224.px", "hooli.murmur50.ENG.32236.fa"])'
  VALUES[QUESTIONS_TO_EXCLUDE_FROM_DRIVER_ANALYSIS] = Array

  COMMENT_REPLIES = "comment_replies"
  DESCRIPTIONS[COMMENT_REPLIES] = "An authorised user is permitted to leave replies on comments"
  VALUES[COMMENT_REPLIES] = [DISABLED, ENABLED]

  TEMPLATES_ACCOUNT = "templates_account"
  DESCRIPTIONS[TEMPLATES_ACCOUNT] = "Designate this account as a template account, allowing surveys to be exported as survey templates"
  VALUES[TEMPLATES_ACCOUNT] = Boolean

  INCLUDE_DEMOGRAPHIC_SPREADS = "include_demographic_spreads"
  DESCRIPTIONS[INCLUDE_DEMOGRAPHIC_SPREADS] = "Set this to false to prevent requesting demographic spread from SRI. Should only be used when large demographics causing timeout or not loading the scores"
  VALUES[INCLUDE_DEMOGRAPHIC_SPREADS] = Boolean

  SEND_ENGAGEMENT_MS_TEAMS_MESSAGES = "send_engagement_ms_teams_messages"
  DESCRIPTIONS[SEND_ENGAGEMENT_MS_TEAMS_MESSAGES] = "Send engagement MS Teams messages"
  VALUES[SEND_ENGAGEMENT_MS_TEAMS_MESSAGES] = [DISABLED, ENABLED]

  SHOW_RESULT_TO_ACTION_GUIDE_TEXT_MS_TEAMS = "show_results_to_action_guide_text_ms_teams"
  DESCRIPTIONS[SHOW_RESULT_TO_ACTION_GUIDE_TEXT_MS_TEAMS] = "Display results to action guide text and link on MS teams notifications"
  VALUES[SHOW_RESULT_TO_ACTION_GUIDE_TEXT_MS_TEAMS] = [DISABLED, ENABLED]

  SURVEY_CAN_SEND_SMS = "survey_can_send_sms"
  DESCRIPTIONS[SURVEY_CAN_SEND_SMS] = "When this is enabled, the survey will be allowed to send sms communications"
  VALUES[SURVEY_CAN_SEND_SMS] = [DISABLED, ENABLED]

  # Returns the global set of flags. This is *static*. It should be considered
  # part of the code baseline.
  def self.global
    @_global ||= Configs::Global.new
  end

  class StandaloneConfiguration
    include Configurable
    attr_reader FIELD_NAME

    def initialize(config = {})
      @configuration = config
    end

    def [](key)
      @configuration[key]
    end
  end

  def self.names
    VALUES.keys
  end

  def self.descriptions
    DESCRIPTIONS
  end

  # Class to be used as the global representation. It's just a static hash
  # under the hood, with the Flaggable module mixed in.
  class Global < StandaloneConfiguration
    def initialize
      super({
        Configs::ALLOWED_CLOCK_DRIFT_IN_SECONDS => 2,
        Configs::ALLOW_GROUP_PARTICIPANTS => ENABLED,
        Configs::CAPTURE_LIKERT_COLOR_SCHEMA => CAPTURE_LIKERT_DEFAULT_COLOR_SCHEMA,
        Configs::CAPTURE_RADIO_THRESHOLD => CAPTURE_DEFAULT_RADIO_THRESHOLD,
        Configs::DATE_FORMAT => "%m/%d/%Y",
        Configs::DAYS_ELIGABLE_AFTER_EXIT => 0,
        Configs::DEMONSTRABLE => false,
        Configs::DEMOGRAPHIC_PRELOAD_LIMIT => 10,
        Configs::HIDE_SECRET_URL => ENABLED,
        Configs::EFFECTIVENESS_360_EMPLOYEE_DRIVEN => DISABLED,
        Configs::EFFECTIVENESS_360_COACHED => ENABLED,
        Configs::ENFORCES_AUTH_FOR_CAPTURE => false,
        Configs::FREE_SURVEY_RESPONSE_LIMIT => FREE_SURVEY_RESPONSE_LIMIT_DEFAULT,
        Configs::KIOSK_CODE => GENERATE_KIOSK_CODE,
        Configs::LIFECYCLE_WORKFLOW => LIFECYCLE_WORKFLOW_FULL,
        Configs::MAX_RAW_DATA_EXTRACT_SIZE => 15_000,
        Configs::MAXIMUM_REPORT_FILTERS => 5,
        Configs::MURMUR_FEEDBACK_SCREEN => ENABLED,
        Configs::PDF_PAGE_SIZE => "A4",
        Configs::RAW_DATA_EXTRACT => DISABLED,
        Configs::REPORT_HIERARCHY_RECURSION => 1,
        Configs::REPORT_SHARING_ADD_ALL_USERS_LIMIT => 1500,
        Configs::REPORT_SHARING_ADD_PARTICIPANTS_LIMIT => 1500,
        Configs::REPORT_SHARING_MULTI_DEMOGRAPHICS_MAX_REPORT_COMBINATIONS => 300,
        Configs::SIGNIFICANCE_COMMENT_POPULATION => 5,
        Configs::SIGNIFICANCE_DRIVER_ANALYSIS => 25,
        Configs::SIGNIFICANCE_LIFECYCLE_POPULATION => 0,
        Configs::SIGNIFICANCE_POPULATION => 5,
        Configs::INDIRECT_IDENTIFICATION_PROTECTION_LEVEL => Reporting::Confidentiality::IndirectIdentificationProtectionLevel::INDIVIDUALS,
        Configs::LIFECYCLE_INDIRECT_IDENTIFICATION_PROTECTION_LEVEL => Reporting::Confidentiality::IndirectIdentificationProtectionLevel::OFF,
        Configs::ALLOW_SLACK_IN_NON_PRODUCTION => DISABLED,
        Configs::SUPPORTED_LOCALES => ["en"],
        Configs::SEND_ENGAGEMENT_EMAILS => Configs::ENABLED,
        Configs::SUPPORTED_PRODUCTS => [],
        Configs::SURVEY_CONFIRMATION_SCREEN => SURVEY_CONFIRMATION_SCREEN_DISABLED,
        Configs::SURVEY_INVITE_BATCH_SIZE => 100,
        Configs::CAN_PICK_ANY_DATE => false,
        Configs::RUN_REPORT_DATA_SERVICE_EXPERIMENT => true,
        Configs::SHOW_THREE_SIXTY_REVIEWER_NAMES => DISABLED,
        Configs::MAXIMUM_REPORT_DEMOGRAPHIC_SPREAD_SIZE => 500,
        Configs::SEND_THREE_SIXTY_EMAILS => ENABLED,
        Configs::COMMENT_REPLIES => ENABLED,
        Configs::TEMPLATES_ACCOUNT => false,
        Configs::INCLUDE_DEMOGRAPHIC_SPREADS => true,
        Configs::SEND_ENGAGEMENT_MS_TEAMS_MESSAGES => ENABLED,
        Configs::SHOW_RESULT_TO_ACTION_GUIDE_TEXT_MS_TEAMS => ENABLED,
        Configs::SURVEY_CAN_SEND_SMS => DISABLED
      })
    end
  end
end
