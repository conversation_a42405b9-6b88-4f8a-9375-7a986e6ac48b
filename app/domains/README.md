# Domains

Welcome to `app/domains/`! This directory contains our application domain logic.
The classes here express our application’s core functionality, and live
independently of the Rails framework.

The classes here also act as an _entry point_ to this application functionality.
As we split our monolith into separate services, or continue our internal
refactoring, these classes can be updated to call upon other
services or interact with events as required.

> If you would like to start a discussion around changing this pattern, please
  open a PR to update this file and engage as many people as possible! Let’s
  make this a living document.

_This directory is not to be confused with `domains/`, which contains the
event sourcing code._

## When should I use `app/domains/`?

`app/domains/` should be the home for all new application domain logic.

This particularly includes any classes that would have otherwise been added to
`app/services/` or `app/actions/`, and even certain areas of `lib/`.

If you’re making significant changes to an existing feature, or refactoring
existing service or action classes, please consider following the guidelines
below and moving these to `app/domains/` whenever possible.

## File Organisation

Nothing should live in the top-level directory (except this README!). We should
organise everything into directories representing the high-level concerns within
our system.

This way, you can easily look at the top level directories to see an overview of
the domain behavior we offer, then drill into a particular folder to learn
specific aspects of that behavior.

This structure should be flat, with the high-level concerns existing as
directories at the top-level only. For example, ”questions” should exist at
`app/domains/questions/` not nested under surveys at
`app/domains/surveys/questions/`.

Under these top-level directories, the primary public interfaces for each domain
are nested within two subdirectories:

- `commands/`, for classes that effect some change or action
- `queries/`, for classes that read data from the system

Each directory should map to its own Ruby module. For example,
`app/domains/questions/` would contain classes exclusively within the
`Questions` module. Like the other Rails-managed directories within `app/`, the
`domains` segment of the file path does _not_ map to a module.

Any supporting code for the command and query classes should live inside the
same domain directory. Further subdirectories (and corresponding module
namespaces) can be used to organise this code.

Here are some exmaple domain directories and their contents:

```
# A cut down example

app/domains/
├── README.md # You are here!
├── participants/
│   └── commands/
│       ├── bulk_invite_participants.rb   # Participants::Commands::BulkInviteParticipants
│       ├── reinvite_participant.rb       # Participants::Commands::ReinviteParticipant
│       └── uninvite_participant.rb       # Participants::Commands::UninviteParticipant
├── responses/
│   ├── commands/
│   │   ├── answer_question.rb            # Responses::Commands::AnswerQuestion
│   │   └── start_response.rb             # Responses::Commands::StartResponse
│   └── queries/
│       │                                 # Responses::Queries::MandatoryQuestionsAnswerableOnSubmit
│       └── mandatory_questions_answerable_on_submit.rb
└── surveys/
    └── commands/
        ├── delete_survey/
        │   └── delete_survey_mongo.rb   # This is an unfortunate outlier because this needs
        │                                  to be called from a MongoDB projector directly for
        │                                  event sourced surveys.
        ├── delete_survey.rb             # Surveys::Commands::DeleteSurvey
        └── launch_survey.rb             # Surveys::Commands::LaunchSurvey
```

### Some guidelines for writing public command and query classes


- **Classes should be namespaced using nested modules**

    For example:

    ```ruby
    module Participants
      module Commands
        class BulkInviteParticipants
          # ...
        end
      end
    end
    ```

    This is for the reasons outlined in the Ruby Style Guide’s [Namespace
    Definition][namespace-style] advice, and to keep our code simpler and more
    reliable by avoiding Rails-provided constant-autoloading behavior.

    n.b. RSpec example groups for these classes should _not_ be defined within
    the same nested namespace (despite many tests within this codebase following
    this approach). Tests should always operate from the “outside” of the
    subject. For example:

    ```ruby
    RSpec.describe Participants::Commands::BulkInviteParticipants do
      # ...
    end
    ```

- **Classes should use constructor [Dependency Injection][di]**

    This allows them to be reused and creates a clear separation between the
    dependencies of the class and arguments (e.g. to `#call` or other instance
    methods) that expose actual behaviour.

- **Methods should accept descriptive keyword arguments**

    This makes it clear what’s being passed in from the caller.

- **Classes should follow the [Single Responsibility Principle][srp]**

    Classes should do a single thing, and have a single reason to change. This
    makes it easy to make changes without affecting anything unrelated. Also:

    - If a class becomes more complicated, you can confidently refactor it (e.g.
      extracting behavior to private methods) while keeping it coherent. In a
      class with numerous responsibilities, this might otherwise result in a
      mishmash of unrelated private methods.
    - It ensures that all injected dependencies are relevant to the class. In a
      class with numerous responsibilities, many dependencies will be required
      but not universally used (for example, launching a survey may need an
      email-related dependency, whereas archiving a survey may not), making the
      class harder to understand and maintain as a whole.

- **Classes should have self-descriptive names**

    Name your command classes using a verb, e.g.
    `Surveys::Commands::CreateBlankSurvey`.

    For queries, use a noun, either singular or plural, as best fits, e.g.
    `SurveyPrograms::Queries::SurveyProgram` or
    `SurveyPrograms::Queries::TitleblockLinks`.

    In using these classes, it may at times feel like there is redundant
    information in the class names, but this helps make things more obvious at
    the call site:

    ```ruby
    create_blank_survey = Surveys::Commands::CreateBlankSurvey.new

    # Somewhere else
    create_blank_survey.call(account: account)
    ```

[namespace-style]: https://github.com/rubocop-hq/ruby-style-guide#namespace-definition
[di]: https://en.wikipedia.org/wiki/Dependency_injection
[srp]: https://en.wikipedia.org/wiki/Single-responsibility_principle

### Commands (`app/domains/**/commands/`)

Structure your command classes like so:

```ruby
module Participants
  module Commands
    class BulkInviteParticipants
      # Dependencies available as attribute readers
      attr_reader :splunk_logger, :command_handler

      # Dependencies specified as keyword params, with default dependencies
      # supplied as the default arguments.
      #
      # These are values that _should not need to change_ over the lifetime
      # of the object.
      def initialize(
        splunk_logger: Splunk::Logger.new,
        command_handler: Domains::SurveyDesign::Participant::InviteCommandHandler.new
      )
        # Capture dependencies as instance variables
        @splunk_logger = splunk_logger
        @command_handler = command_handler
      end

      # Arguments to call (or other methods) exist only for the lifetime of method
      # execution, they are NOT saved as instance variables
      def call(survey_id:, responses:, executor:, correlation_id:)
        command = # ...
        metadata = # ...

        # Dependencies referred to via attribute readers
        command_handler.call(command, metadata)

        splunk_logger.log(msg: "foo")
      end
    end
  end
end
```

_These are not to be confused with event sourcing commands. Although this example
shows a class calling event sourcing commands, this will not always be the case._

### Queries (`app/domains/**/queries/`)

Structure your query classes like so:

```ruby
module UserSurveyPreferences
  module Queries
    class GetPreference
      include Dry::Monads[:result]

      def initialize(
        repo: Domains::MurmurGeneral.database(:general_db)[:user_survey_preferences],
        return_model: UserSurveyPreferences::Models::Preference
      )
        @repo = repo
        @return_model = return_model
      end

      def call(user_id:, survey_id:)
        orm_result = repo.where(user_id: user_id, survey_id: survey_id).first

        return Failure(:not_found) unless orm_result

        return_result = return_model.new(
          report_type: orm_result.report_type
        )

        Success(return_result)
      end
    end
  end
end
```

As we're moving away from returning Mongoid models, queries should always
return a dry struct  that wraps fetched database data. In the example above
the repo is the database, which in this case has been switched from Mongo
to Postgres. The postgres result is then wrapped in a the dry struct defined
in the `return_model`. This approach allows for easy swapping of data sources
to help in the push away from MongoDB.

Having these queries can give us a nice interface to our data while also
providing an easy place to switch out to a different data source as needed, for
example, switching to read a projection built from our event sourced data.
