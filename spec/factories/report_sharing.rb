FactoryBot.define do
  factory :data, class: Hash do
    skip_create
    initialize_with { attributes[:data] }
  end
end

FactoryBot.define do
  factory :title_block_data, parent: :data do
    survey_id { "1234" }

    data do
      {
        "title_block": {
          "breadcrumb_url": "/surveys/#{survey_id}/reports/simplified_report_configuration",
          "default_action_url": "/surveys/#{survey_id}/reports/admin/insight_report"
        }
      }
    end
  end
end

FactoryBot.define do
  factory :available_report_types_data, parent: :data do
    include_simplified_insights { true }
    report_types { %w[summary standard advanced] }
    with_ai_comment_summaries { false }

    data do
      {
        "available_report_types": [
          {
            "report_type": "summary",
            "options": [
              {
                "key": "includeParticipation",
                "readonly": true,
                "is_selected": true
              },
              {
                "key": "includeSimplifiedInsights",
                "readonly": false,
                "is_selected": include_simplified_insights
              }
            ]
          },
          {
            "report_type": "standard",
            "options": [
              {
                "key": "includeActionFramework",
                "readonly": false,
                "is_selected": true
              },
              {
                "key": "includeComments",
                "readonly": false,
                "is_selected": false
              },
              {
                "key": "includeParticipation",
                "readonly": true,
                "is_selected": true
              },
              {
                "key": "includeSimplifiedInsights",
                "readonly": true,
                "is_selected": true
              },
              {
                "key": "includeInsights",
                "readonly": true,
                "is_selected": true
              },
              {
                "key": "includeQuestions",
                "readonly": true,
                "is_selected": true
              }
            ]
          },
          {
            "report_type": "advanced",
            "options": [
              {
                "key": "includeActionFramework",
                "readonly": false,
                "is_selected": true
              },
              {
                "key": "includeComments",
                "readonly": false,
                "is_selected": true
              },
              {
                "key": "includeParticipation",
                "readonly": true,
                "is_selected": true
              },
              {
                "key": "includeSimplifiedInsights",
                "readonly": true,
                "is_selected": true
              },
              {
                "key": "includeInsights",
                "readonly": true,
                "is_selected": true
              },
              {
                "key": "includeQuestions",
                "readonly": true,
                "is_selected": true
              },
              {
                "key": "includeHeatmap",
                "readonly": true,
                "is_selected": true
              },
              {
                "key": "includeCustomHeatmap",
                "readonly": true,
                "is_selected": true
              }
            ]
          }
        ]
      }.tap do |data|
        if with_ai_comment_summaries
          [1, 2].each do |index|
            data[:available_report_types][index][:options].insert(2, {
              key: "includeAICommentSummaries",
              readonly: false,
              is_selected: false
            })
          end
        end
        data[:available_report_types].select! { |key| report_types.include?(key[:report_type]) }
      end
    end
  end
end

FactoryBot.define do
  factory :survey_data, parent: :data do
    id { "1234" }
    aggregate_id { "abcde" }
    name { "Basic Survey" }
    status { :design }
    type { "standard" }
    employee_hierarchy { nil }

    data do
      {
        survey: {
          id: id.to_s,
          aggregate_id: aggregate_id,
          name: name,
          status: status.to_s,
          type: type,
          employee_hierarchy: employee_hierarchy.nil? ? nil : {
            id: employee_hierarchy[:id].to_s,
            stq_id: employee_hierarchy[:stq_id].to_s
          }
        }.compact
      }
    end
  end
end

# for the report_props method within EditReportConfigurationPageConstructor
FactoryBot.define do
  factory :report_props_data, parent: :data do
    id { "1234" }
    name { "Report 1" }
    description { nil }
    type { "standard" }
    selected_demographic_id { "" }
    selected_demographic_filters { [] }
    is_hierarchy_report { false }
    multi_demographic_report { false }
    hierarchy_level { nil }
    direct_reports_only { nil }
    leader_filter_enabled { nil }
    selected_report_type_options { [] }
    report_scope { nil }

    data do
      {report: {
        id: id.to_s,
        name: name,
        description: description,
        type: type,
        selected_report_type_options: selected_report_type_options,
        selected_demographic_id: selected_demographic_id,
        selected_demographic_filters: selected_demographic_filters,
        report_structure: calculate_report_structure,
        hierarchy_level: (hierarchy_level if is_hierarchy_report),
        direct_reports_only: (direct_reports_only if is_hierarchy_report),
        leader_filter_enabled: (leader_filter_enabled if is_hierarchy_report),
        report_scope: report_scope
      }.compact}
    end
  end
end

FactoryBot.define do
  factory :metadata, parent: :data do
    report_count { 0 }
    default_report_type { "standard" }
    max_report_combinations { 300 }

    data do
      {metadata: {report_count: report_count, default_report_type: default_report_type, max_report_combinations: max_report_combinations}}
    end
  end
end

private

def calculate_report_structure
  if is_hierarchy_report
    "hierarchy"
  elsif multi_demographic_report
    "demographicVariations"
  else
    "demographic"
  end
end
