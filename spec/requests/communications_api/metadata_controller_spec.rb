require "rails_helper"

module CommunicationsApi
  RSpec.describe "metadata api" do
    let(:account) do
      Account.create!(
        name: "surveyemail",
        region: Region.all.first,
        subdomain: "surveyemail"
      )
    end
    let(:survey) do
      Survey.create!(
        name: "some survey with emails",
        type: :engagement,
        account: account,
        configuration: {"supported_locales" => ["en", "fr"]}
      )
    end
    let(:user) { FactoryBot.create(:admin, account: account) }

    before do
      survey.assign_flag!(Flags::IMPROVED_COMMS_CONFIGURATION, Flags::ENABLED)
      sign_in user
    end

    describe "GET /communications_api/surveys/:survey_id/metadata/supported_locales" do
      it "returns json list of the supported locale codes and their labels" do
        get("/communications_api/surveys/#{survey.id}/metadata/supported_locales")

        expect(response.status).to eq(200)

        supported_locales_data = JSON.parse(response.body)
        expect(supported_locales_data).to include({"code" => "en", "label" => "English (US English)"})
        expect(supported_locales_data).to include({"code" => "fr", "label" => "Français (French)"})
      end
    end

    describe "GET /communications_api/surveys/:survey_id/metadata/sidebar_navigation_menu" do
      it "returns updated sidebar prop" do
        get("/communications_api/surveys/#{survey.id}/metadata/sidebar_navigation_menu")

        expect(response.status).to eq(200)

        updated_sidebar_data = JSON.parse(response.body)
        expect(updated_sidebar_data).to eq({"updatedSidebar" => true, "surveyAggregateId" => survey.aggregate_id, "accountId" => account.id.to_s})
      end
    end

    describe "#admin_emails" do
      let(:user) { FactoryBot.create(:admin, preferred_name: "Shmicheal", email: "<EMAIL>", account: account) }

      it "return JSON value with admin emails" do
        get admin_emails_communications_api_survey_metadata_path(survey)
        expect(response.status).to eq 200

        admin_email_data = JSON.parse(response.body)

        expect(admin_email_data).to eq [{"email" => user.email, "name" => user.preferred_name}]
      end

      context "when a user is a survey admin" do
        it "returns survey admin emails" do
          some_user = Person.create!(name: "user", preferred_name: "Shmuser", email: "<EMAIL>", password: "password123!", account: account)
          some_user.enable_survey_admin_access!(survey)
          get admin_emails_communications_api_survey_metadata_path(survey)
          admin_email_data = JSON.parse(response.body)
          expect(admin_email_data).to eq [{"email" => user.email, "name" => user.preferred_name}, {"email" => some_user.email, "name" => some_user.preferred_name}]
        end
      end

      context "when account admin is also a survey admin" do
        it "does not return duplicate emails" do
          user.enable_survey_admin_access!(survey)
          get admin_emails_communications_api_survey_metadata_path(survey)
          admin_email_data = JSON.parse(response.body)
          expect(admin_email_data).to eq [{"email" => user.email, "name" => user.preferred_name}]
        end
      end

      context "when an admin does not have a valid email" do
        it "does not include that admin" do
          FactoryBot.create(:admin, name: "nilemail", preferred_name: "Shnilemail", email: nil, account: account)
          FactoryBot.create(:admin, name: "emptyemail", preferred_name: "Shemptyemail", email: "", account: account)
          get admin_emails_communications_api_survey_metadata_path(survey)
          admin_email_data = JSON.parse(response.body)
          expect(admin_email_data).to eq [{"email" => user.email, "name" => user.preferred_name}]
        end
      end

      context "when an admin does not have a preferred name" do
        let(:user) { FactoryBot.create(:admin, name: "name", email: "<EMAIL>", account: account) }
        it "uses the admin name" do
          get admin_emails_communications_api_survey_metadata_path(survey)
          admin_email_data = JSON.parse(response.body)
          expect(admin_email_data).to eq [{"email" => user.email, "name" => user.name}]
        end
      end

      context "when a user is not an admin" do
        it "does not include that user" do
          Person.create!(name: "Shmuser", preferred_name: "Shmuser", email: "<EMAIL>", password: "password123!", account: account)
          get admin_emails_communications_api_survey_metadata_path(survey)
          admin_email_data = JSON.parse(response.body)
          expect(admin_email_data).to eq [{"email" => "<EMAIL>", "name" => "Shmicheal"}]
        end
      end

      context "when a user has the surveys_admin role" do
        it "includes users with surveys_admin role in the response" do
          # Create a regular user (not account admin or survey admin)
          surveys_admin_user = FactoryBot.create(:person,
            preferred_name: "Surveys Admin",
            email: "<EMAIL>",
            account: account)

          # Mock the Roles::Queries::FindUsersByRole to return our user
          find_users_by_role = instance_double(Roles::Queries::FindUsersByRole)
          expect(Roles::Queries::FindUsersByRole).to receive(:new).and_return(find_users_by_role)
          expect(find_users_by_role).to receive(:call)
            .with(account_id: account.aggregate_id.to_s, role_key: :surveys_admin)
            .and_return([surveys_admin_user])

          get admin_emails_communications_api_survey_metadata_path(survey)
          expect(response.status).to eq 200

          admin_email_data = JSON.parse(response.body)

          # Response should include both the account admin (user) and the surveys_admin user
          expect(admin_email_data).to include(
            {"email" => user.email, "name" => user.preferred_name},
            {"email" => surveys_admin_user.email, "name" => surveys_admin_user.preferred_name}
          )
        end
      end

      context "when users have multiple admin roles" do
        it "does not include duplicate entries for users with multiple roles" do
          # Create a user who is both a survey admin and has surveys_admin role
          multi_role_user = FactoryBot.create(:person,
            preferred_name: "Multi Role",
            email: "<EMAIL>",
            account: account)

          # Make them a survey admin
          multi_role_user.enable_survey_admin_access!(survey)

          # Mock the Roles::Queries::FindUsersByRole to also return this user
          find_users_by_role = instance_double(Roles::Queries::FindUsersByRole)
          expect(Roles::Queries::FindUsersByRole).to receive(:new).and_return(find_users_by_role)
          expect(find_users_by_role).to receive(:call)
            .with(account_id: account.aggregate_id.to_s, role_key: :surveys_admin)
            .and_return([multi_role_user])

          get admin_emails_communications_api_survey_metadata_path(survey)
          expect(response.status).to eq 200

          admin_email_data = JSON.parse(response.body)

          # The multi_role_user should appear only once in the response
          matching_entries = admin_email_data.select { |entry| entry["email"] == multi_role_user.email }
          expect(matching_entries.length).to eq(1)

          # The response should include both the account admin and the multi-role user
          expect(admin_email_data).to include(
            {"email" => user.email, "name" => user.preferred_name},
            {"email" => multi_role_user.email, "name" => multi_role_user.preferred_name}
          )
        end
      end
    end

    context "when the user is a Surveys Admin" do
      let(:user) { FactoryBot.create(:surveys_admin, account: account) }

      before do
        sign_in user
      end

      it "returns a 200 response" do
        get admin_emails_communications_api_survey_metadata_path(survey)
        expect(response.status).to eq(200)
      end
    end

    context "when the user is a Superuser for Account" do
      let(:user) { FactoryBot.create(:superuser_for_account, granted_account: account) }

      before do
        sign_in user
      end

      it "returns a 200 response" do
        get admin_emails_communications_api_survey_metadata_path(survey)
        expect(response.status).to eq(200)
      end
    end
  end
end
