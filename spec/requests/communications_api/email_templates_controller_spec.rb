require "rails_helper"

module CommunicationsApi
  RSpec.describe "email templates api" do
    let(:account) do
      Account.create!(
        name: "surveyemail",
        region: Region.all.first,
        subdomain: "surveyemail"
      )
    end
    let(:survey) do
      Survey.create!(
        name: "some survey with emails",
        type: :engagement,
        account: account
      )
    end
    let(:user) { FactoryBot.create(:admin, account: account) }
    let(:another_user) { FactoryBot.create(:admin, account: account) }
    let(:non_admin_user) { FactoryBot.create(:user, account: account) }

    let(:s3_client) { Aws::S3::Client.new(stub_responses: true) }
    let(:aws_region) { "Melbourne" }
    let(:bucket_name) { "sent-emails" }

    before do
      allow(Aws::S3::Client).to receive(:new).and_return(s3_client)
      allow(ENV).to receive(:fetch).and_call_original
      allow(ENV).to receive(:[]).and_call_original
      allow(ENV).to receive(:fetch).with("AWS_REGION").and_return(aws_region)
      allow(ENV).to receive(:fetch).with("SENT_EMAIL_DOCUMENTS_BUCKET_2_NAME").and_return(bucket_name)
      survey.assign_flag!(Flags::IMPROVED_COMMS_CONFIGURATION, Flags::ENABLED)
      sign_in user
    end

    shared_examples "calling raw_content endpoint with correct permissions" do
      it "returns a 200 response" do
        CommunicationTemplate.create!(
          survey_id: survey.id,
          type: "invite",
          subject: "survey invite",
          content: [
            {
              "type" => "paragraph",
              "content" => [
                {
                  "type" => "text",
                  "text" => "hi there"
                }
              ]
            }
          ]
        )
        get("/communications_api/surveys/#{survey.id}/email_templates/invite/raw_content")
        expect(response.status).to eq(200)
      end
    end

    describe "GET /communications_api/surveys/:survey_id/email_templates/:template_type/raw_content" do
      context "user is a surveys admin" do
        let(:user) { FactoryBot.create(:surveys_admin, account: account) }
        before do
          sign_in user
        end
        it_behaves_like "calling raw_content endpoint with correct permissions"
      end
      context "user is a survey admin" do
        let(:user) { FactoryBot.create(:survey_admin, account: account, survey: survey) }
        before do
          sign_in user
        end
        it_behaves_like "calling raw_content endpoint with correct permissions"
      end
      context "user is a superuser" do
        let(:user) { FactoryBot.create(:superuser_for_account, granted_account: account) }
        before do
          sign_in user
        end
        it_behaves_like "calling raw_content endpoint with correct permissions"
      end
      it "returns json representation of the content of the template, the subject and the allowed variables" do
        CommunicationTemplate.create!(
          survey_id: survey.id,
          type: "invite",
          subject: "survey invite",
          content: [
            {
              "type" => "paragraph",
              "content" => [
                {
                  "type" => "text",
                  "text" => "hi there"
                }
              ]
            }
          ]
        )
        get("/communications_api/surveys/#{survey.id}/email_templates/invite/raw_content")

        expect(response.status).to eq(200)

        communication_template_data = JSON.parse(response.body)
        expect(communication_template_data).to include(
          "content" => [
            {
              "type" => "paragraph",
              "content" => [
                {
                  "type" => "text",
                  "text" => "hi there"
                }
              ]
            }
          ]
        )
        expect(communication_template_data).to include(
          "subject" => "survey invite"
        )
        expect(communication_template_data["allowed_variables"]).to include(
          {"id" => "survey_name", "label" => "Survey name", "link" => false, "sample_value" => survey.name}
        )
        confidentiality_link_variable = communication_template_data["allowed_variables"].find { |v| v["id"] == "confidentiality_link" }
        expect(confidentiality_link_variable).to be nil
      end

      context "for a user with a blank preferred name" do
        let(:preferred_name) { "" }
        let(:name) { "Demo name" }
        let(:user) { FactoryBot.create(:admin, account: account, preferred_name: preferred_name, name: name) }

        it "returns json representation of the content of the template, the subject and the allowed variables" do
          CommunicationTemplate.create!(
            survey_id: survey.id,
            type: "invite",
            subject: "survey invite",
            content: [
              {
                "type" => "paragraph",
                "content" => [
                  {
                    "type" => "text",
                    "text" => "hi there"
                  }
                ]
              }
            ]
          )
          get("/communications_api/surveys/#{survey.id}/email_templates/invite/raw_content")

          expect(response.status).to eq(200)

          communication_template_data = JSON.parse(response.body)
          expect(communication_template_data["allowed_variables"]).to include(
            {"id" => "recipient_name", "label" => "Recipient name", "link" => false, "sample_value" => name}
          )
        end
      end

      context "for a user with a preferred name" do
        let(:preferred_name) { "Preferred demo name" }
        let(:name) { "Demo name" }
        let(:user) { FactoryBot.create(:admin, account: account, preferred_name: preferred_name, name: name) }

        it "returns json representation of the content of the template, the subject and the allowed variables" do
          CommunicationTemplate.create!(
            survey_id: survey.id,
            type: "invite",
            subject: "survey invite",
            content: [
              {
                "type" => "paragraph",
                "content" => [
                  {
                    "type" => "text",
                    "text" => "hi there"
                  }
                ]
              }
            ]
          )
          get("/communications_api/surveys/#{survey.id}/email_templates/invite/raw_content")

          expect(response.status).to eq(200)

          communication_template_data = JSON.parse(response.body)
          expect(communication_template_data["allowed_variables"]).to include(
            {"id" => "recipient_name", "label" => "Recipient name", "link" => false, "sample_value" => preferred_name}
          )
        end
      end
    end

    describe "GET /communications_api/surveys/:survey_id/email_templates/:template_type/subject" do
      it "returns the rendered subject for the template" do
        CommunicationTemplate.create!(
          survey_id: survey.id,
          type: "invite",
          subject: [
            {
              "type" => "text",
              "text" => "invite for "
            },
            {
              "type" => "emailVariable",
              "attrs" => {
                "variable" => "survey_name"
              }
            }
          ]
        )
        get("/communications_api/surveys/#{survey.id}/email_templates/invite/subject")

        expect(response.status).to eq(200)

        communication_template_subject = JSON.parse(response.body)
        expect(communication_template_subject).to include(
          "subject" => "invite for some survey with emails"
        )
      end
    end

    describe "POST /communications_api/surveys/:survey_id/email_templates/invite/send_test" do
      it "sends a test email with the content of that template" do
        CommunicationTemplate.create!(
          survey_id: survey.id,
          type: "invite",
          subject: [
            {type: "text", text: "dear "},
            {type: "emailVariable", attrs: {variable: "recipient_name"}}
          ]
        )
        put("/communications_api/surveys/#{survey.id}/email_templates/invite/send_test", params: {user_emails: [user.email, another_user.email, non_admin_user.email]})
        expect(response.status).to eq(200)

        comms = ActionMailer::Base.deliveries
        expect(comms.count).to eq(2)
        subjects = comms.pluck(:subject)
        email_recipients = comms.pluck(:to)
        expect(subjects.include?("dear #{user.name}"))
        expect(subjects.include?("dear #{another_user.name}"))
        expect(email_recipients.include?(user.email))
        expect(email_recipients.include?(another_user.email))
      end

      context "for surveys with improved comms disabled" do
        before do
          survey.assign_flag!(Flags::IMPROVED_COMMS_CONFIGURATION, Flags::DISABLED)
        end

        it "also sends an email" do
          template = CommunicationTemplate.create!(
            type: "invite",
            segments: [
              CommunicationSegment.new(
                type: "subject",
                text_content: "dear %{recipient_name}"
              ),
              CommunicationSegment.new(
                type: "message",
                html_content: "<h1>hi there</h1>"
              )
            ]
          )
          survey_period = SurveyPeriod.create!(
            survey_id: survey.id
          )
          survey_period.communication_designs << CommunicationDesign.new(
            communication_template_id: template.id
          )
          survey_period.save!

          put("/communications_api/surveys/#{survey.id}/email_templates/invite/send_test")
          expect(response.status).to eq(200)

          comms = ActionMailer::Base.deliveries
          expect(comms.count).to eq(1)
          expect(comms.first.subject).to eq("dear #{user.name}")
        end
      end

      it "sends emails to all types of admins (account, survey, and surveys admins)" do
       # Setup: Create a template
       CommunicationTemplate.create!(
         survey_id: survey.id,
         type: "invite",
         subject: [
           {type: "text", text: "Invite Subject"}
         ],
         content: [
           {
             "type" => "paragraph",
             "content" => [
               {"type" => "text", "text" => "Invite Content"}
             ]
           }
         ]
       )

       # Setup: Create users with different admin roles
       account_admin = user # Use existing account admin
       survey_admin = FactoryBot.create(:survey_admin, account: account, survey: survey)

       # Create a surveys_admin that will be found by the query
       surveys_admin = FactoryBot.create(:user, account: account)

       # Stub the Roles query to return the surveys_admin user
       find_users_by_role = instance_double(Roles::Queries::FindUsersByRole)
       allow(Roles::Queries::FindUsersByRole).to receive(:new).and_return(find_users_by_role)
       allow(find_users_by_role).to receive(:call)
         .with(account_id: account.aggregate_id.to_s, role_key: :surveys_admin)
         .and_return([surveys_admin])

       # Include all admin emails in the request
       put("/communications_api/surveys/#{survey.id}/email_templates/invite/send_test",
         params: {user_emails: [account_admin.email, survey_admin.email, surveys_admin.email]})

       # Verify response
       expect(response.status).to eq(200)

       # Verify that emails were sent to all three types of admins
       comms = ActionMailer::Base.deliveries
       expect(comms.count).to eq(3)

       recipients = comms.flat_map(&:to)
       expect(recipients).to include(account_admin.email)
       expect(recipients).to include(survey_admin.email)
       expect(recipients).to include(surveys_admin.email)

       # Verify the Roles query was called
       expect(find_users_by_role).to have_received(:call)
         .with(account_id: account.aggregate_id.to_s, role_key: :surveys_admin)
     end
    end

    describe "GET /communications_api/surveys/:survey_id/email_templates/invite" do
      it "returns the rendered content of the template as html" do
        CommunicationTemplate.create!(
          survey_id: survey.id,
          type: "invite",
          subject: [
            {"type" => "text", "text" => "dear "},
            {"type" => "emailVariable", "attrs" => {"variable" => "recipient_name"}}
          ],
          content: [
            {
              "type" => "paragraph",
              "content" => [
                {"type" => "text", "text" => "time for "},
                {"type" => "emailVariable", "attrs" => {"variable" => "survey_name"}}
              ]
            }
          ],
          restricted_content: [
            {
              "type" => "paragraph",
              "content" => [
                {"type" => "text", "text" => "instructions"}
              ]
            }
          ]
        )
        get("/communications_api/surveys/#{survey.id}/email_templates/invite")
        expect(response.status).to eq(200)

        doc = parse_html(response.body)
        assert_select doc, "p", text: "time for some survey with emails"
      end

      def parse_html(html_string)
        Nokogiri::HTML::Document.parse(html_string)
      end
    end

    describe "PUT /communications_api/surveys/:survey_id/email_templates/invite" do
      it "updates the content and subject of the communication" do
        communication = CommunicationTemplate.create!(
          survey_id: survey.id,
          type: "invite"
        )

        put("/communications_api/surveys/#{survey.id}/email_templates/invite", **{
          params: {
            content: [{type: "text", text: "new content"}],
            subject: [{type: "text", text: "new subject"}]
          }
        })
        expect(response.status).to eq(200)

        updated_communication = CommunicationTemplate.find(communication.id)
        expect(updated_communication.content).to eq([
          {"type" => "text", "text" => "new content"}
        ])
        expect(updated_communication.subject).to eq([
          {"type" => "text", "text" => "new subject"}
        ])
      end
    end
  end
end
