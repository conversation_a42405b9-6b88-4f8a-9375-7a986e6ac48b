require "rails_helper"

RSpec.describe FiltersController do
  let(:account) { FactoryBot.create(:simple_account) }
  let(:demographic) { FactoryBot.create(:segment_question) }
  let(:demographic_stq) { FactoryBot.build(:survey_to_question, question: demographic) }
  let(:option) { demographic.select_options.last }
  let(:report) { FactoryBot.create(:report, survey: survey, base_demographic_stq_id: demographic_stq.id) }
  let(:superuser) { FactoryBot.create(:superuser, account: account) }
  let(:count) { 2 }
  let(:overall) { 20 }
  let(:manager) do
    FactoryBot.create(:user, account_id: account.id) do |manager|
      report.access_grants.create!(report_consumer: manager, select_option_id: option.id)
    end
  end
  let(:has_access_to_view_multi_demographic_reports) { false }

  describe "#data", vcr: true do
    let(:account) { Account.subdomain("hooli") }
    let(:admin) { FactoryBot.create(:admin, account: account) }
    let(:data) { JSON.parse(response.body) }

    before do
      allow_any_instance_of(ReportSharing::Queries::MultiDemographicFeatureFlag)
        .to receive(:has_access_to_view_multi_demographic_reports?)
        .and_return(has_access_to_view_multi_demographic_reports)
    end

    context "with the engagement survey" do
      let(:survey) { account.surveys.find_by!(name: "Hooli's Engagement Survey") }

      let(:department_stq_id) do
        survey.survey_to_questions.where(type: :segment).detect { |stq| stq.question.name == "Department" }.id.to_s
      end

      before do
        resign_in admin
        VCR.use_cassette("filters/data") do
          get :data, params: {survey_id: survey.id, report_id: "admin", demographic: department_stq_id}
        end
      end

      it "returns enabled in options list", requires_legacy_test_data: true do
        department_options = data["options"].detect { |option| option["id"] == department_stq_id }["options"]
        expect(department_options).to include(
          a_hash_including(
            "text" => "Legal"
          )
        )
      end

      it "returns an empty anchor list", requires_legacy_test_data: true do
        expect(data["anchor"]).to eq([])
      end
    end

    context "with the hierarchy survey" do
      let(:survey) { account.surveys.find_by!(name: "Hooli's Hierarchy Survey") }
      let(:manager_stq) do
        survey.survey_to_questions.where(type: :segment).detect do |s|
          s.question.name == "Manager Email"
        end
      end

      before do
        resign_in admin
        VCR.use_cassette("filters/data_for_hierarchy") do
          get :data, params: {old_manager_insight_id: survey.id}
        end
      end

      it "contains a hierarchy in the anchor data", requires_legacy_test_data: true do
        expect(data["anchor"]).to include(
          a_hash_including(
            "text" => "Abagail Hirthe MD"
          )
        )
      end
    end

    context "when the SRE is down" do
      let(:survey) { account.surveys.find_by!(name: "Hooli's Engagement Survey") }
      let(:sri_service) { double(SnapshotReportingEngineService, participation_result: participation_result) }
      let(:participation_result) { double(Monad::Result, error?: true) }

      before do
        resign_in admin
        allow(sri_service).to receive(:without_hierarchy_privacy) { sri_service }
        allow(SnapshotReportingEngineService).to receive(:new).and_return(sri_service)
        get :data, params: {survey_id: survey.id, report_id: "admin"}
      end

      it "returns an empty hash", requires_legacy_test_data: true do
        expect(data).to be_empty
      end
    end

    context "when the demographic stq id is passed in" do
      let(:survey) { account.surveys.find_by!(name: "Hooli's Engagement Survey") }
      let(:demographic_stq_id) { survey.survey_to_questions.segment.detect { |stq| stq.question.name == "Department" }.id.to_s }
      before do
        resign_in admin
        VCR.use_cassette("filters/single_demographic_data") do
          get :data, params: {survey_id: survey.id, report_id: "admin", demographic: demographic_stq_id}
        end
      end

      it "only returns the filtered option", requires_legacy_test_data: true do
        expect(data["options"].size).to eq 1
        expect(data["options"].first["id"]).to eq demographic_stq_id
      end
    end

    context "with multi-demographic report and multiple anchors" do
      let(:account) { FactoryBot.create(:simple_account) }
      let(:has_access_to_view_multi_demographic_reports) { true }
      let(:master_survey) { FactoryBot.create(:master_survey, account: account, name: "Master") }
      let(:survey) { FactoryBot.create(:basic_survey, account: account) }
      let(:department) { FactoryBot.create(:segment_question, name: "Department", account: account) }
      let(:location) { FactoryBot.create(:segment_question, name: "Location", account: account) }
      let(:tenure) { FactoryBot.create(:segment_question, name: "Tenure", account: account) }
      let(:department_stq) {
        master_survey.survey_to_questions.create(question: department, type: department.type)
        survey.survey_to_questions.create(question: department, type: department.type)
      }
      let(:location_stq) {
        master_survey.survey_to_questions.create(question: location, type: location.type)
        survey.survey_to_questions.create(question: location, type: location.type)
      }
      let(:tenure_stq) {
        master_survey.survey_to_questions.create(question: tenure, type: tenure.type)
        survey.survey_to_questions.create(question: tenure, type: tenure.type)
      }
      let(:department_option) { department.select_options.first }
      let(:location_option) { location.select_options.first }
      let(:tenure_option) { tenure.select_options.first }
      let(:report_scope) do
        {
          department_stq.id.to_s => [department_option.id],
          location_stq.id.to_s => [location_option.id]
        }
      end
      let(:report) do
        FactoryBot.create(
          :report,
          survey: survey,
          base_demographic_stq_id: nil,
          report_scope: report_scope,
          filter_demographic_stq_ids: [department_stq.id, location_stq.id, tenure_stq.id]
        )
      end
      let(:admin) { FactoryBot.create(:admin, account: account) }

      before do
        resign_in admin
        VCR.use_cassette("filters/multi_demographic_data", erb: {
          survey_id: survey.id.to_s,
          stq_id_1: department_stq.id.to_s,
          stq_id_2: location_stq.id.to_s,
          so_id_1: department_option.id.to_s,
          so_id_2: location_option.id.to_s,
          spread_stq_id: tenure_stq.id.to_s,
          spread_so_id: tenure_option.id.to_s
        }, allow_playback_repeats: true) do
          get :data, params: {
            survey_id: survey.id,
            report_id: report.id,
            demographic: tenure_stq.id.to_s,
            a: "#{department_stq.id}-#{department_option.id},#{location_stq.id}-#{location_option.id}"
          }
        end
      end

      # TODO: need to fix demographic filter props to return anchor as an array of anchor params
      pending "returns anchor as an array of anchor params" do
        data = JSON.parse(response.body)
        expect(data["anchor"]).to match_array([
          "#{department_stq.id}-#{department_option.id}",
          "#{location_stq.id}-#{location_option.id}"
        ])
      end

      it "returns options for all demographics in the report scope" do
        data = JSON.parse(response.body)
        expect(data.dig("options", 0, "id")).to eq tenure_stq.id.to_s
        expect(data.dig("options", 0, "text")).to eq "Tenure"
      end
    end
  end
end
