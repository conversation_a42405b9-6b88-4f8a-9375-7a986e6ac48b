railsEnvironment: staging # maps to RAILS_ENV
configName: ragdoll # the value to pull murmur env values out of from config/dotenv/${env}/${configName}

auroraCluster: true
dbMigration: false
eventProjectors: true
demographicEventProjectors: false
employeeEventReader: false
employeeEventReaderKafka: false
perfDimensionKafkaConsumer: false

serviceGateway: true
murmurAlbDnsName: lb.ragdoll.murmur.usw2.dev-us.cultureamp.io

employeeEcstConsumerEnabled: false
demographicsEcstConsumerEnabled: false
demographicValuesEcstConsumerEnabled: false
importsEcstConsumerEnabled: false

murmurGeneralAccountErasureKafkaConsumer:
  desiredCount: 1
  autoScalingMinCapacity: 1
  autoScalingMaxCapacity: 1

murmurGeneralAccountErasureTopic: "ragdoll.account-data.accounts.v1"
murmurGeneralAccountErasureGroupIdPrefix: "ragdoll-murmur."
murmurGeneralAccountErasureTopicClientId: "ragoll-murmur-racecar"

vaultAddress: https://vault-development.usw2.dev-us.cultureamp.io

railsConsole:
  instanceType: t3.small

murmurAssumableRoles:
  - arn:aws:iam::************:role/development-us-cultureamp-MurmurEmployeeDataCrossAc-NkuX31QQvphq # S3EmployeeDataBucketRoleArn
  - arn:aws:iam::************:role/cultureamp-development-data-MurmurCrossAccountRole-193CEJPO32K19 # S3DataExportRoleArn

employeesEventReader:
  memoryMiB: 4096

eventProjector:
  memoryMiB: 4096

batchDelayedJob:
  - name: uninterruptible_long
    batchType: FARGATE
    desiredCount: 1

delayedJob:
  - name: staging
    cpu: 512
    memoryMiB: 1024
  - name: intercom_updates
    cpu: 512
    memoryMiB: 1024
  - name: uninterruptible_short
    cpu: 512
    memoryMiB: 1024
    allowGracefulExit: true
