railsEnvironment: staging # maps to RAILS_ENV
configName: ibex # the value to pull murmur env values out of from config/dotenv/${env}/${configName}

auroraCluster: true
dbMigration: false
eventProjectors: true
demographicEventProjectors: false
employeeEventReader: false
employeeEventReaderKafka: false
perfDimensionKafkaConsumer: false
employeeEcstConsumerEnabled: false
demographicsEcstConsumerEnabled: false
demographicValuesEcstConsumerEnabled: false
importsEcstConsumerEnabled: false

vaultAddress: https://vault-development.usw2.dev-us.cultureamp.io

railsConsole:
  instanceType: t3.small

murmurAssumableRoles:
  - arn:aws:iam::************:role/development-us-cultureamp-MurmurEmployeeDataCrossAc-NkuX31QQvphq # S3EmployeeDataBucketRoleArn
  - arn:aws:iam::************:role/cultureamp-development-data-MurmurCrossAccountRole-193CEJPO32K19 # S3DataExportRoleArn

employeesEventReader:
  memoryMiB: 2048

eventProjector:
  memoryMiB: 4096

serviceGateway: true
murmurAlbDnsName: lb.ibex.murmur.usw2.dev-us.cultureamp.io

delayedJob:
  - name: staging
    cpu: 512
    memoryMiB: 1024
  - name: intercom_updates
    cpu: 512
    memoryMiB: 1024
  - name: survey_migration
    desiredCount: 0
    cpu: 2048
    memoryMiB: 4096
  - name: uninterruptible_short
    cpu: 512
    memoryMiB: 1024
    allowGracefulExit: true
