type: object
properties:
  selectOptionId:
    type: string
    format: uuid
    example: 4b492219-3915-42cf-9cef-ca5e930c97cb
  valueTranslations:
    type: array
    items:
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      required:
        - text
        - locale
    example:
      - text: Option 1
        locale: en
      - text: Opción 1
        locale: es
  sortTerm:
    type: object
    example:
      en: "00001"
    nullable: true