type: object
properties:
  demographicId:
    description: The aggregate ID of the demographic.
    type: string
    format: uuid
    example: 4b492219-3915-42cf-9cef-ca5e930c97cb
  demographicMongoId:
    description: The mongo ID of the survey-to-question that links this demographic
    type: string
    example: 67ff0b44437b38a664cd7a37
  descriptionTranslations:
    description: The text of the demographic description in different locales.
    type: array
    items:
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      required: [ text, locale ]
      example:
        - text: Demographic 1
          locale: en
        - text: Pregunta 1
          locale: es
  nameTranslations:
    description: The text of the demographic name in different locales.
    type: array
    items:
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      required: [ text, locale ]
      example:
        - text: Demographic 1
          locale: en
        - text: Pregunta 1
          locale: es
  demographicValues:
    description: The values that can be assigned for this demographic.
    type: array
    items:
      $ref: "../schemas/demographicValue.yaml"
required:
  - demographicId
  - demographicMongoId
  - descriptionTranslations
  - nameTranslations
  - demographicValues
