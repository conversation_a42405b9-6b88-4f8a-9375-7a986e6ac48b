type: object
properties:
  questionId:
    description: The aggregate ID of the question.
    type: string
    format: uuid
    example: 4b492219-3915-42cf-9cef-ca5e930c97cb
  surveyToQuestionId:
    description: The Mongo ID of the survey to question that links this question
    type: string
    example: 67ff0b44437b38a664cd7a37
  questionMongoId:
    description: The Mongo ID of this question
    type: string
    format: uuid
    example: 4b492219-3915-42cf-9cef-ca5e930c97cb
  sectionId:
    description: The aggregate ID of the section which this question(stq) belongs.
    type: string
    format: uuid
    example: 4b492219-3915-42cf-9cef-ca5e930c97cb
  factorIds:
    description: The aggregate ID of the factors which this question(stq) belongs.
    type: array
    items:
      type: string
      format: uuid
      example: 4b492219-3915-42cf-9cef-ca5e930c97cb
  descriptionTranslations:
    description: The text of the question in different locales.
    type: array
    items:
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      required: [ text, locale ]
      example:
        - text: Question 1
          locale: en
        - text: Pregunta 1
          locale: es
  nameTranslations:
    description: The name of the question in different locales.
    type: array
    items:
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      required: [ text, locale ]
      example:
        - text: Question 1
          locale: en
        - text: Pregunta 1
          locale: es
  type:
    description: The intended use of this question.
    type: string
    enum:
      - culture
      - segment
      - classify
      - interview
      - reviewer_notes
      - outcome
      - dimension
    example: culture
  questionType:
    description: What kind of answer the question accepts.
    type: string
    enum:
      - free-text
      - rating
      - select
    example: rating
  ratingScale:
    description: The rating scale of the question.
    type: string
    enum:
      - agreement
      - rating
      - satisfaction
      - quality
      - frequency
      - frequency_alt
      - performance
      - importance
      - focus
    example: agreement
  selectOptions:
    description: The options for the select question.
    type: array
    items:
      $ref: "../schemas/selectOption.yaml"
  intendedPurpose:
    description: The intended purpose of the question.
    type: string
    enum:
      - classification
      - demographic
      - feedback
      - reviewer_notes
      - interview
      - outcome
    example: classification
  stqOrder:
    description: The order of the stq that belongs to this question.
    type: integer
    example: 1
    nullable: true
  selectionLimit:
    description: The selection limit of the select question. If the question is not a select question, this field should be null
    type: integer
    example: 3
    nullable: true
required:
  - questionId
  - surveyToQuestionId
  - questionMongoId
  - sectionId
  - descriptionTranslations
  - nameTranslations
  - type
  - questionType
  - intendedPurpose
  - stqOrder
  - selectionLimit
