{"name": "murmur", "version": "1.0.0", "license": "UNLICENSED", "private": true, "description": "", "main": "", "directories": {"doc": "doc"}, "packageManager": "yarn@1.22.22", "scripts": {"preinstall": "bin/check-if-logged-in-to-github-npm-registry.sh", "build-webpack": "webpack --config config/development.webpack.config.js", "jest-bin": "bin/jest", "jest:debug": "JEST_DEBUG=true yarn jest-bin", "test": "yarn compile:i18n && yarn jest", "test-ci": "yarn test --ci --coverage --forceExit", "test:watch": "yarn test --watch", "typecheck": "tsc --project tsconfig.cli.json --noemit", "storybook:dev": "storybook dev -p 3456", "storybook:tests": "test-storybook", "storybook:build": "yarn compile:i18n && yarn storybook build --stats-json --output-dir .storybook/build", "eslint-all": "eslint . --ext .js,.jsx,.ts,.tsx", "extract-translations": "yarn i18n-extract -s '{app,lib}/client/**/*.{ts,tsx}' -o ./config/frontend-locales/en.json -d", "compile:i18n": "ts-node ./bin/compile-i18n.ts", "postinstall": "if [ -f bin/node-installed-packages-record ]; then bin/node-installed-packages-record update-after-install; fi", "check-install-up-to-date": "if [ -f bin/node-installed-packages-record ]; then bin/node-installed-packages-record assert-install-is-up-to-date; fi", "ts-node": "TS_NODE_TRANSPILE_ONLY=true ts-node", "generate-elm-translations": "bin/generate-elm-translations.sh", "stylelint": "stylelint '{app,lib}/**/*.{scss,css}'", "chromatic": "npx chromatic --project-token=102e040be58b --storybook-build-dir .storybook/build/ --only-changed --exit-zero-on-changes --auto-accept-changes master", "prepare": "husky install", "component": "yarn ui component", "extraction": "yarn ui extraction", "hook": "yarn ui hook", "page": "yarn ui page", "provider": "yarn ui provider", "resource": "yarn ui resource", "check-for-duplicate-yarn-lock-dependencies": "yarn-deduplicate --fail --list --exclude @types/node d3-array d3-scale d3-time react-router @types/react-router -- yarn.lock", "deduplicate-yarn-lock": "yarn-deduplicate --exclude @types/node d3-array d3-scale d3-time react-router -- yarn.lock", "locale:push": "yarn i18n-smartling-push", "locale:check-extraction": "yarn i18n-check-clean-extraction"}, "repository": {"type": "git", "url": "**************:cultureamp/murmur.git"}, "author": "", "//": {"//": "Known-outdated packages:", "autoprefixer": "Requires Node 10+ and will need thorough testing. https://trello.com/c/f709PKRB", "jest-cli": "Version 24 has a lot of breaking changes, and fails over half our test suite. https://trello.com/c/sN7e4PuA", "jquery": "2.x and 3.x contain breaking changes: https://trello.com/c/R4U3yfA0", "react-markdown": "3.x does not yet have a way to turn new lines into <br> tags", "react-select-fast-filter-options": "Using a fork that has moved react-select to devDependencies", "react-select-old": "2.x is a breaking change. https://trello.com/c/iml8nDIG", "redux": "Redux 4 contains breaking changes: https://trello.com/c/NCT5yL5n", "scroll-into-view-if-needed": "V2 has breaking changes: https://trello.com/c/QZQpsIie", "select2": "4.x contains breaking changes: https://trello.com/c/AhK1IzQH"}, "dependencies": {"@apollo/client": "^3.10.8", "@babel/runtime": "^7.23.5", "@bugsnag/js": "^6.5.2", "@bugsnag/plugin-react": "^6.5.0", "@cultureamp/admin-navbar": "^1.0.0", "@cultureamp/ai-shared-ui": "^0.8.1", "@cultureamp/amplitude": "^3.5.20", "@cultureamp/analytics": "1.0.5", "@cultureamp/drawer-stencil-experiment-react": "^0.7.25", "@cultureamp/duplicate-survey-confirmation-modal": "^3.0.23", "@cultureamp/fetch": "^4.0.2", "@cultureamp/frontend-apis": "^12.0.0", "@cultureamp/frontend-services": "^2.1.0", "@cultureamp/goals-shared-ui": "^2.4.0", "@cultureamp/i18n-react-intl": "^2.5.9", "@cultureamp/intercom": "^5.4.1", "@cultureamp/kaizen-elm-component-library": "^12.6.1", "@cultureamp/kaizen-elm-draft-button": "^5.6.13", "@cultureamp/kaizen-elm-draft-card": "^1.7.4", "@cultureamp/kaizen-elm-draft-divider": "^1.9.4", "@cultureamp/kaizen-elm-draft-dropdown": "^1.6.11", "@cultureamp/kaizen-elm-draft-empty-state": "^3.3.16", "@cultureamp/kaizen-elm-draft-events": "^1.5.11", "@cultureamp/kaizen-elm-draft-form": "^5.6.12", "@cultureamp/kaizen-elm-draft-loading-placeholder": "^1.10.11", "@cultureamp/kaizen-elm-draft-modal": "^8.4.20", "@cultureamp/kaizen-elm-draft-popover": "^3.5.2", "@cultureamp/kaizen-elm-draft-select": "^1.24.1", "@cultureamp/kaizen-elm-draft-split-button": "^3.4.10", "@cultureamp/kaizen-elm-draft-table": "^3.12.14", "@cultureamp/kaizen-elm-draft-tag": "^2.3.17", "@cultureamp/kaizen-elm-draft-title-block": "^4.4.20", "@cultureamp/kaizen-elm-draft-tooltip": "^3.7.10", "@cultureamp/kaizen-elm-draft-user-interactions": "^1.5.11", "@cultureamp/kaizen-elm-draft-well": "^3.7.11", "@cultureamp/kaizen-elm-hosted-assets": "^1.2.1", "@cultureamp/kaizen-elm-notification": "^0.6.16", "@cultureamp/kaizen-elm-progress-bar": "^1.3.14", "@cultureamp/redirect-to-login": "^2.0.3", "@cultureamp/reports-components": "^14.1.3", "@cultureamp/skills-coach": "0.7.16", "@cultureamp/survey-admin-sidebar": "^3.4.1", "@cultureamp/unified-navigation-ui": "^7.5.0", "@datadog/browser-rum": "^5.28.0", "@hello-pangea/dnd": "^16.5.0", "@icelab/defo": "^0.0.3", "@kaizen/brand": "^1.5.9", "@kaizen/brand-moment": "^1.13.9", "@kaizen/button": "^3.0.6", "@kaizen/component-library": "^16.8.2", "@kaizen/components": "^1.77.3", "@kaizen/deprecated-component-library-helpers": "^2.5.8", "@kaizen/design-tokens": "^10.3.16", "@kaizen/draft-avatar": "^2.8.41", "@kaizen/draft-badge": "^1.13.13", "@kaizen/draft-button": "^6.1.18", "@kaizen/draft-card": "3.2.12", "@kaizen/draft-collapsible": "^3.5.41", "@kaizen/draft-divider": "^2.2.12", "@kaizen/draft-empty-state": "^5.3.0", "@kaizen/draft-form": "^10.5.2", "@kaizen/draft-guidance-block": "^5.1.7", "@kaizen/draft-illustration": "^6.1.2", "@kaizen/draft-likert-scale-legacy": "^1.11.1", "@kaizen/draft-menu": "^5.1.1", "@kaizen/draft-modal": "^10.6.24", "@kaizen/draft-page-layout": "^2.3.14", "@kaizen/draft-popover": "^5.1.4", "@kaizen/draft-select": "^2.10.41", "@kaizen/draft-split-button": "^4.1.4", "@kaizen/draft-table": "^5.9.6", "@kaizen/draft-tabs": "^5.2.44", "@kaizen/draft-tag": "^3.5.1", "@kaizen/draft-tile": "^5.10.8", "@kaizen/draft-title-block-zen": "^7.11.0", "@kaizen/draft-tooltip": "^5.2.4", "@kaizen/draft-vertical-progress-step": "^1.9.4", "@kaizen/draft-well": "^4.3.8", "@kaizen/hosted-assets": "^2.0.3", "@kaizen/loading-skeleton": "^1.7.3", "@kaizen/loading-spinner": "^2.3.12", "@kaizen/notification": "^1.7.1", "@kaizen/progress-bar": "^2.3.34", "@kaizen/select": "^6.21.2", "@kaizen/split-button": "^1.3.52", "@kaizen/typography": "^2.4.0", "@tanstack/react-query": "^4.36.1", "@tanstack/react-query-devtools": "^4.36.1", "@webcomponents/custom-elements": "^1.6.0", "@webcomponents/webcomponentsjs": "^2.8.0", "autosize": "^4.0.2", "autosize-input": "^1.0.2", "axios": "^1.7.4", "bowser": "^2.11.0", "classnames": "^2.5.1", "color": "^3.1.3", "color-string": "^1.9.1", "copy-to-clipboard": "^3.3.3", "core-js": "^3.34.0", "d3-array": "^2.11.0", "d3-axis": "^1.0.12", "d3-force": "^2.0.1", "d3-format": "^1.4.3", "d3-scale": "^4.0.2", "d3-selection": "^1.4.1", "d3-shape": "^1.3.7", "d3-time": "^3.1.0", "date-fns": "^4.1.0", "diff": "^5.1.0", "dompurify": "^3.0.0", "elm-upgrade-shims": "cultureamp/elm-upgrade-shims#0.5.1", "empty": "^0.10.1", "eventemitter3": "^4.0.7", "file-saver": "^2.0.5", "fuzzy": "^0.1.3", "glob": "^8.1.0", "global": "^4.4.0", "graphql": "^16.8.2", "history": "^4.10.1", "html2canvas": "^1.4.1", "immutable": "^3.8.2", "jest-canvas-mock": "^2.5.2", "jquery": "^1.12.4", "js-search": "^2.0.1", "jsdom": "^24.0.0", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.45", "msw": "^1.2.1", "myzod": "^1.11.0", "nano-memoize": "^1.3.1", "p-queue": "^6.6.2", "plotly.js": "^2.28.0", "pluralize": "^8.0.0", "postcss": "^8.4.39", "prop-types": "^15.8.1", "prosemirror-commands": "^1.5.2", "prosemirror-history": "^1.4.1", "prosemirror-keymap": "^1.2.2", "prosemirror-model": "^1.22.3", "prosemirror-state": "^1.4.3", "prosemirror-transform": "^1.9.0", "prosemirror-view": "^1.33.9", "query-string": "^6.14.1", "react": "^18.2.0", "react-animate-height": "^2.1.2", "react-animate-on-change": "^2.2.0", "react-collapse": "^4.0.3", "react-dates": "^21.8.0", "react-display-name": "^0.2.5", "react-dom": "^18.2.0", "react-dropzone": "^14.1.1", "react-elm-components": "^1.1.0", "react-focus-lock": "^2.12.1", "react-highlight-words": "^0.20.0", "react-intl": "^6.6.8", "react-markdown": "^10.1.0", "react-media": "^1.10.0", "react-motion": "^0.5.2", "react-phone-number-input": "3.1.12", "react-plotly.js": "^2.6.0", "react-prop-types": "^0.4.0", "react-redux": "^8.1.3", "react-router": "5.3.4", "react-router-dom": "5.3.4", "react-select-fast-filter-options": "^0.2.3", "react-select-old": "npm:react-select@1.3.0", "react-sizeme": "^3.0.2", "react-spring": "^9.7.4", "react-syntax-highlighter": "^15.6.1", "react-tooltip": "4.5.1", "redux": "^3.7.2", "redux-thunk": "^2.3.0", "regenerator-runtime": "^0.13.11", "runtypes": "^5.0.1", "scroll-into-view": "^1.15.0", "scroll-into-view-if-needed": "^2.2.26", "select2": "^3.5.1", "standard-error": "^1.1.0", "tailwindcss": "^3.4.14", "td-js-sdk": "^2.1.0", "ts.data.json": "^1.5.0", "uberfetch": "^1.0.1", "url": "^0.11.4", "use-resize-observer": "^8.0.0", "uuid": "^3.3.3", "whatwg-fetch": "^3.6.20"}, "devDependencies": {"@babel/core": "^7.23.5", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.23.5", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-function-sent": "^7.23.3", "@babel/plugin-proposal-json-strings": "^7.18.6", "@babel/plugin-proposal-numeric-separator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-proposal-throw-expressions": "^7.23.3", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-transform-flow-strip-types": "^7.23.3", "@babel/plugin-transform-modules-commonjs": "^7.23.3", "@babel/plugin-transform-proto-to-assign": "^7.23.3", "@babel/plugin-transform-runtime": "^7.23.4", "@babel/preset-env": "^7.23.5", "@babel/preset-flow": "^7.23.3", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@chromatic-com/storybook": "^3.2.4", "@cultureamp/engagement-component": "^2.0.14", "@cultureamp/frontend-build": "^6.0.2", "@cultureamp/frontend-build-elm": "^6.0.0", "@cultureamp/frontend-core": "^3.0.3", "@cultureamp/frontend-testing": "3.1.6", "@cultureamp/next-config": "^3.2.4", "@cultureamp/next-storybook": "^4.1.2", "@kaizen/tailwind": "^1.2.14", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.15", "@storybook/addon-styling-webpack": "^1.0.1", "@storybook/addon-webpack5-compiler-babel": "^3.0.5", "@storybook/react-webpack5": "^8.5.2", "@storybook/test": "^8.5.2", "@testing-library/jest-dom": "^6.4.8", "@testing-library/react-hooks": "^8.0.1", "@types/autosize": "^4.0.3", "@types/babel__core": "^7.20.5", "@types/color": "^3.0.6", "@types/d3-array": "^3.2.1", "@types/d3-axis": "^3.0.6", "@types/d3-force": "^3.0.10", "@types/d3-format": "^3.0.4", "@types/d3-scale": "^4.0.8", "@types/d3-selection": "^3.0.10", "@types/d3-shape": "^3.1.6", "@types/diff": "^5.0.9", "@types/file-saver": "^2.0.7", "@types/html-minifier": "^4.0.5", "@types/immutable": "^3.8.7", "@types/intercom-web": "^2.8.26", "@types/jest": "^29.5.0", "@types/jquery": "^1.10.35", "@types/js-beautify": "^1.14.3", "@types/loadable__component": "^5.13.9", "@types/loader-utils": "^2.0.6", "@types/plotly.js": "^2.33.3", "@types/pluralize": "^0.0.33", "@types/prettier": "^1.19.1", "@types/prosemirror-commands": "^1.3.0", "@types/prosemirror-history": "^1.3.0", "@types/prosemirror-keymap": "^1.2.0", "@types/prosemirror-model": "^1.17.0", "@types/prosemirror-state": "^1.4.0", "@types/prosemirror-view": "^1.24.0", "@types/query-string": "^5.1.0", "@types/react": "^18.2.60", "@types/react-dates": "^21.8.6", "@types/react-dom": "^18.2.19", "@types/react-highlight-words": "0.20.0", "@types/react-motion": "^0.0.31", "@types/react-phone-number-input": "^3.0.13", "@types/react-plotly.js": "^2.6.3", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "@types/react-select-old": "npm:@types/react-select@1.3.6", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-test-renderer": "^16.9.12", "@types/redux-logger": "^3.0.13", "@types/scroll-into-view": "^1.16.0", "@types/webpack": "^5.28.5", "@types/yargs": "^17.0.33", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "absolute-path": "0.0.0", "amp-has-class": "^1.0.3", "autoprefixer": "^9.8.8", "babel-core": "^7.0.0-bridge", "babel-eslint": "^10.1.0", "babel-loader": "^8.3.0", "babel-plugin-macros": "^3.1.0", "babel-plugin-tsconfig-paths-module-resolver": "^1.0.4", "chromatic": "^11.18.1", "clipboard": "^2.0.11", "cors": "^2.8.5", "css-loader": "^5.2.7", "css-minimizer-webpack-plugin": "^5.0.1", "css-modules-loader-core2": "0.0.1", "elm": "^0.19.1-4", "elm-analyse": "stil4m/elm-analyse#701b8b4013a4f057b9382d368e42adc6fe08e14e", "elm-format": "^0.8.6", "elm-i18next-gen": "^1.1.0", "elm-test": "0.19.1-revision9", "es-check": "^7.1.1", "eslint": "^7.32.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-import-resolver-webpack": "^0.13.8", "eslint-plugin-flowtype": "^4.7.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.34.4", "eslint-plugin-react-hooks": "^4.6.2", "expose-loader": "^4.0.0", "fetch-mock": "^9.11.0", "file-loader": "^6.2.0", "flow-bin": "^0.108.0", "focus-visible": "^5.2.1", "html-minifier": "^4.0.0", "husky": "^8.0.3", "identity-obj-proxy": "^3.0.0", "image-webpack-loader": "^8.1.0", "immutable-devtools": "0.1.5", "indent-string": "^4.0.0", "inspect-react-element": "^1.1.1", "jest": "29.7.0", "jest-cli": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-watch-typeahead": "0.6.3", "js-beautify": "^1.14.11", "loader-utils": "^1.4.2", "mini-css-extract-plugin": "^2.6.1", "msw-storybook-addon": "^1.10.0", "node-sass": "9.0.0", "postcss": "^8.4.39", "postcss-custom-properties": "^10.0.0", "postcss-initial": "^3.0.4", "postcss-loader": "^4.3.0", "prettier": "^2.8.8", "react-docgen-typescript-plugin": "^1.0.8", "react-refresh": "^0.14.2", "react-select-event": "^5.5.1", "react-shallow-testutils": "^3.0.1", "react-test-renderer": "^18.2.0", "redux-logger": "^3.0.6", "redux-mock-store": "^1.5.4", "resize-observer-polyfill": "^1.5.1", "sass-loader": "^13.2.2", "style-loader": "^4.0.0", "stylelint": "^15.11.0", "stylelint-config-standard-scss": "^11.1.0", "terser-webpack-plugin": "^4.2.3", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsconfig-paths-webpack-plugin": "^4.2.0", "typescript": "^5.2.2", "util": "^0.12.5", "utility-types": "^3.10.0", "webpack": "^5.91.0", "webpack-assets-manifest": "^5.1.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.9.2", "webpack-merge": "^6.0.1", "webpack-retry-chunk-load-plugin": "^3.1.1", "webpack-sources": "^1.4.3", "yalc": "^1.0.0-pre.53", "yargs": "^17.7.2", "yarn-deduplicate": "^6.0.2"}, "peerDependencies": {"msw": "1.2.1"}, "resolutions": {"@types/react": "^18.2.60", "cross-spawn": "^7.0.6", "msw": "^1.2.1", "node-fetch": "^2.6.7", "lodash": "^4.17.21", "empty/envify/jstransform/commoner/detective/acorn": "^5.7.4", "postcss-gap-properties": "^3.0.5", "ssri": "^8.0.1", "webpack-dev-server/selfsigned/node-forge": "^1.3.1", "@babel/runtime": "^7.23.5", "node-sass": "^9.0.0", "query-string/decode-uri-component": "0.2.2", "@cultureamp/unified-navigation-ui/@cultureamp/amplitude": "^3.5.20", "@cultureamp/unified-navigation-ui/**/jsrsasign": "^11.1.0", "react-elm-components/**/ua-parser-js": "0.7.33", "@cultureamp/unified-navigation-ui/@cultureamp/frontend-apis/**/undici": "^5.28.5", "es5-ext": "^0.10.64", "micromatch": "4.0.8", "braces": "3.0.3", "ws": "8.17.1", "playwright": "^1.45.0", "color-string": "1.9.1"}}