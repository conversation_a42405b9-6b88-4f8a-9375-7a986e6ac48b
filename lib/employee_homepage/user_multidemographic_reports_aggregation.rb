module EmployeeHomepage
  class UserMultidemographicReportsAggregation
    attr_reader :user, :account

    def initialize(
      user:,
      account:
    )
      @user = user
      @account = account
      @tracer = Datadog.tracer
    end

    def all_reports
      can_query_all_engagement_surveys? ?
        all_reports_for_account
        :
        all_reports_for_user
    end

    private

    def can_query_all_engagement_surveys?
      Authorization.permitted?(
        user_id: user.aggregate_id,
        resource_id: account.aggregate_id,
        permission: Account::Permissions::ADMINISTER_SURVEY_REPORTS
      )
    end

    def all_reports_for_user
      @tracer.trace("all_reports_for_user") do
        user_id = @user.id
        account_id = @account.id
        account_aggregate_id = @account.aggregate_id
        is_hrbp = @user.hr_business_partner?

        pipeline = [
          # Get all the current users report grants with action_framework_role viewer/report_owner, except those that are deleted.
          {
            "$match" => {
              "report_consumer_id" => user_id,
              "deleted_at" => nil,
              "action_framework_role" => {"$in" => [ReportAccessGrant::VIEWER, ReportAccessGrant::REPORT_OWNER]}
            }
          },
          # Join the report grants with the reports collection, we skip deleted reports or unshared ones
          {
            "$lookup" => {
              "from" => "reports",
              "as" => "report",
              "localField" => "report_id",
              "foreignField" => "_id",
              "pipeline" => [
                {
                  "$match" => {
                    "deleted_at" => nil,
                    "$or" => [
                      {"$and" => [
                        {"$expr" => {"$eq" => [is_hrbp, false]}},
                        {"sharing_status" => "published"}
                      ]},
                      {
                        "$expr" => {"$eq" => [is_hrbp, true]}
                      }
                    ]
                  }
                }
              ]
            }
          },
          # Each report grant will only match with one report. We unwind the array to get the report object
          {
            "$unwind" => {
              "path" => "$report"
            }
          },
          {
            "$addFields" => {
              "access" => {
                "$switch" => {
                  "branches" => [
                    {
                      "case" => {
                        "$eq" => [
                          "$report.report_view", "participation"
                        ]
                      },
                      "then" => "participation"
                    }, {
                      "case" => {
                        "$eq" => [
                          "$report.report_view", "summary"
                        ]
                      },
                      "then" => "summary"
                    }
                  ],
                  "default" => "viewer"
                }
              },
              "stq_ids" => {
                "$cond" => [
                  {
                    "$eq" => [
                      "$report.base_demographic_stq_id", nil
                    ]
                  }, {
                    "$map" => {
                      "input" => {
                        "$objectToArray" => "$report.report_scope"
                      },
                      "as" => "scope",
                      "in" => {
                        "$toObjectId" => {
                          "$getField" => {
                            "input" => "$$scope",
                            "field" => "k"
                          }
                        }
                      }
                    }
                  }, [
                    "$report.base_demographic_stq_id"
                  ]
                ]
              }
            }
          },
          {
            "$lookup" => {
              "from" => "surveys",
              "localField" => "report.survey_id",
              "foreignField" => "_id",
              "as" => "survey",
              "let" => {
                "stq_ids" => "$stq_ids",
                "select_option_ids" => "$select_option_ids"
              },
              "pipeline" => [
                {
                  "$match" => {
                    "deleted_at" => nil
                  }
                }, {
                  "$project" => {
                    "survey_to_question" => {
                      "$cond" => [
                        {
                          "$ne" => [
                            {
                              "$size" => "$$stq_ids"
                            }, 0
                          ]
                        }, {
                          "$filter" => {
                            "input" => "$survey_to_questions",
                            "as" => "survey_to_questions",
                            "cond" => {
                              "$and" => [
                                {
                                  "$eq" => [
                                    "$$survey_to_questions.status", "active"
                                  ]
                                }, {
                                  "$in" => [
                                    "$$survey_to_questions._id", "$$stq_ids"
                                  ]
                                }
                              ]
                            }
                          }
                        }, []
                      ]
                    },
                    "_id" => 1,
                    "name" => 1
                  }
                }, {
                  "$unwind" => {
                    "path" => "$survey_to_question",
                    "preserveNullAndEmptyArrays" => true
                  }
                }, {
                  "$lookup" => {
                    "from" => "questions",
                    "localField" => "survey_to_question.question_id",
                    "foreignField" => "_id",
                    "as" => "question",
                    "pipeline" => [
                      {
                        "$match" => {
                          "status" => "active"
                        }
                      }
                    ]
                  }
                }, {
                  "$unwind" => {
                    "path" => "$question",
                    "preserveNullAndEmptyArrays" => true
                  }
                }, {
                  "$project" => {
                    "name" => 1,
                    "_id" => 1,
                    "survey_to_questions" => {
                      "id" => {
                        "$toString" => "$survey_to_question._id"
                      },
                      "employee_hierarchy_id" => "$survey_to_question.employee_hierarchy_id",
                      "select_options" => {
                        "$cond" => [
                          {
                            "$eq" => [
                              "$$select_option_ids", nil
                            ]
                          }, nil, {
                            "$map" => {
                              "input" => {
                                "$filter" => {
                                  "input" => "$question.select_options",
                                  "as" => "select_option",
                                  "cond" => {
                                    "$in" => [
                                      "$$select_option._id", "$$select_option_ids"
                                    ]
                                  }
                                }
                              },
                              "as" => "scope",
                              "in" => {
                                "$toString" => {
                                  "$getField" => {
                                    "input" => "$$scope",
                                    "field" => "_id"
                                  }
                                }
                              }
                            }
                          }
                        ]
                      },
                      "question" => {
                        "hierarchy_type" => "$question.hierarchy_type"
                      }
                    }
                  }
                }, {
                  "$unwind" => {
                    "path" => "$survey_to_questions.select_options",
                    "preserveNullAndEmptyArrays" => true
                  }
                }, {
                  "$group" => {
                    "_id" => "$_id",
                    "matched_stq_ids" => {
                      "$push" => {"$toObjectId" => "$survey_to_questions.id"}
                    },
                    "matched_select_option_ids" => {
                      "$push" => {
                        "$toObjectId" => "$survey_to_questions.select_options"
                      }
                    },
                    "survey_to_questions" => {
                      "$push" => "$survey_to_questions"
                    }
                  }
                }
              ]
            }
          },
          # Each report will only match with one survey. We unwind the array to get the survey object
          {
            "$unwind" => {
              "path" => "$survey"
            }
          },
          # Join the stq to it's employee hierarchy. This is expensive so we try to keep the number of STQs low
          {
            "$lookup" => {
              "from" => "reporting_employee_hierarchies",
              "let" => {
                "reportLevels" => "$report.hierarchy_levels",
                "selectOptionIds" => "$select_option_ids"
              },
              "localField" => "survey.survey_to_questions.employee_hierarchy_id",
              "foreignField" => "_id",
              "as" => "employee_hierarchy",
              "pipeline" => [
                {
                  "$match" => {
                    "deleted_at" => nil
                  }
                }, {
                  "$project" => {
                    "_id" => 0,
                    "selectedLevels" => {
                      "$first" => {
                        "$filter" => {
                          "input" => {
                            "$objectToArray" => "$levels"
                          },
                          "as" => "levels",
                          "cond" => {
                            "$and" => [
                              {
                                "$in" => [
                                  {
                                    "$toInt" => "$$levels.k"
                                  }, "$$reportLevels"
                                ]
                              }
                            ]
                          },
                          "limit" => 1
                        }
                      }
                    }
                  }
                }, {
                  "$project" => {
                    "selectedLevels" => {
                      "$map" => {
                        "input" => {
                          "$filter" => {
                            "input" => "$selectedLevels.v",
                            "as" => "hierarchies",
                            "cond" => {
                              "$and" => [
                                {
                                  "$in" => [
                                    {
                                      "$toObjectId" => "$$hierarchies"
                                    }, "$$selectOptionIds"
                                  ]
                                }
                              ]
                            }
                          }
                        },
                        "as" => "hierarchy",
                        "in" => {"$toObjectId" => "$$hierarchy"}
                      }
                    }
                  }
                }
              ]
            }
          },
          # Unwind the employee hierarchy array. This creates a row for each level in the hierarchy
          {
            "$unwind" => {
              "path" => "$employee_hierarchy",
              "preserveNullAndEmptyArrays" => true
            }
          },
          # Validate the report_grant, report and survey.
          # If it's a hierarchy report, we check that the base_demographic_stq question that we matched has employee hierarchy_type. We also
          # check that the select options in the report grant match those in the hierarchy

          # # If it's not a hierarchy report, we check that if the base_demographic_stq exists, that it matches the stq on the survey.
          {
            "$match" => {
              "$or" => [
                {
                  "$and" => [
                    {
                      "report.is_hierarchy_report" => false
                    }, {
                      "$expr" => {
                        "$eq" => [
                          {
                            "$size" => "$stq_ids"
                          }, 0
                        ]
                      }
                    }, {
                      "$expr" => {
                        "$eq" => [
                          {
                            "$size" => "$select_option_ids"
                          }, 0
                        ]
                      }
                    }
                  ]
                },
                {
                  "$and" => [
                    {
                      "report.is_hierarchy_report" => false
                    }, {
                      "$expr" => {
                        "$setEquals" => [
                          "$select_option_ids", {"$ifNull" => ["$survey.matched_select_option_ids", []]}
                        ]
                      }
                    }, {
                      "$expr" => {
                        "$setEquals" => [
                          "$stq_ids", {"$ifNull" => ["$survey.matched_stq_ids", []]}
                        ]
                      }
                    }
                  ]
                }, {
                  "$and" => [
                    {
                      "report.is_hierarchy_report" => true
                    }, {
                      "survey.survey_to_questions.question.hierarchy_type" => :employee
                    }, {
                      "$expr" => {
                        "$setEquals" => [
                          "$select_option_ids", "$employee_hierarchy.selectedLevels"
                        ]
                      }
                    }
                  ]
                }
              ]
            }
          },
          {
            "$unionWith" => {
              "coll" => "responses",
              "pipeline" => [
                {
                  "$match" => {
                    "reviewer_id" => user_id
                  }
                }, {
                  "$group" => {
                    "_id" => "$survey_id",
                    "survey" => {
                      "$first" => {
                        "_id" => "$survey_id"
                      }
                    },
                    "access" => {
                      "$first" => "reviewer"
                    }
                  }
                }, {
                  "$lookup" => {
                    "from" => "surveys",
                    "as" => "survey",
                    "localField" => "survey._id",
                    "foreignField" => "_id"
                  }
                }, {
                  "$unwind" => {
                    "path" => "$survey"
                  }
                }, {
                  "$match" => {
                    "survey.type" => {"$nin" => ["three_sixty", "master", "engagement"]}
                  }
                }
              ]
            }
          },
          # Get any survey_topics that the user is the subject, observer or interviewer for.
          {
            "$unionWith" => {
              "coll" => "survey_topics",
              "pipeline" => [
                {
                  "$match" => {
                    "$or" => [
                      {
                        "subject_id" => user_id,
                        "status" => {"$ne" => "deleted", "$in" => ["shared", "interviewed", "closed"]}
                      }, {
                        "observer_ids" => {"$in" => [user_id]},
                        "status" => {"$ne" => "deleted", "$in" => ["shared", "interviewed", "closed"]}

                      }, {
                        "interviewer_id" => user_id,
                        "status" => {"$ne" => "deleted"}
                      }
                    ],
                    "demonstration" => {"$ne" => true}
                  }
                },
                # Join the users collection to get the subject name
                {
                  "$lookup" => {
                    "from" => "users",
                    "as" => "user",
                    "localField" => "subject_id",
                    "foreignField" => "_id"
                  }
                }, {
                  "$unwind" => {
                    "path" => "$user"
                  }
                }, {
                  "$project" => {
                    "surveyId" => "$survey_id",
                    "subjectName" => "$user.name",
                    "access" => {
                      "$mergeObjects" => [
                        # We treat subject/observer the same way and we want subject access to override observer access.
                        {
                          "$cond" => [
                            {
                              "$in" => [
                                user_id, {
                                  "$ifNull" => [
                                    "$observer_ids", []
                                  ]
                                }
                              ]
                            }, {
                              "access1" => "observer"
                            }, nil
                          ]
                        },
                        {
                          "$cond" => [
                            {
                              "$eq" => [
                                "$subject_id", user_id
                              ]
                            }, {
                              "access1" => "subject"
                            }, nil
                          ]
                        },
                        # If the access is interviewer, the user get access to the insight report
                        {
                          "$cond" => [
                            {
                              "$eq" => [
                                "$interviewer_id", user_id
                              ]
                            }, {
                              "access3" => "interviewer"
                            }, nil
                          ]
                        }
                      ]
                    },
                    "processId" => "$_id"
                  }
                }, {
                  "$project" => {
                    "access" => {
                      "$objectToArray" => "$access"
                    },
                    "subjectName" => 1,
                    "surveyId" => 1
                  }
                }, {
                  "$unwind" => {
                    "path" => "$access"
                  }
                }, {
                  "$project" => {
                    # Interviewer access doesn't need the processId
                    "processId" => {
                      "$cond" => [
                        {
                          "$eq" => [
                            "$access.v", "interviewer"
                          ]
                        }, "$$REMOVE", "$_id"
                      ]
                    },
                    "access" => "$access.v",
                    # Interviewer access doesn't need the subjectName
                    "subjectName" => {
                      "$cond" => [
                        {
                          "$eq" => [
                            "$access.v", "interviewer"
                          ]
                        }, "$$REMOVE", "$subjectName"
                      ]
                    },
                    "survey" => {
                      "_id" => "$surveyId"
                    }
                  }
                }, {
                  "$lookup" => {
                    "from" => "surveys",
                    "as" => "survey",
                    "localField" => "survey._id",
                    "foreignField" => "_id"
                  }
                }, {
                  "$unwind" => {
                    "path" => "$survey"
                  }
                }, {
                  "$match" => {
                    "$or" => [
                      {
                        "$and" => [
                          {
                            "access" => {
                              "$in" => [
                                "subject", "interviewer"
                              ]
                            }
                          }, {
                            "survey.type" => "three_sixty"
                          }
                        ]
                      }, {
                        "access" => "observer"
                      }
                    ]
                  }
                }
              ]
            }
          },
          # Get any survey_admin_grants
          {
            "$unionWith" => {
              "coll" => "survey_admin_grants",
              "pipeline" => [
                {
                  "$match" => {
                    "person_id" => user_id,
                    "overall_admin" => true
                  }
                }, {
                  "$project" => {
                    "survey" => {
                      "_id" => "$survey_id"
                    },
                    "access" => "admin"
                  }
                }
              ]
            }
          },
          {
            "$group" => {
              "_id" => {
                "surveyId" => "$survey._id",
                "processId" => "$processId",
                "subjectName" => "$subjectName"
              },
              "surveyId" => {
                "$first" => "$survey._id"
              },
              "access" => {
                # We get the last access, they are in a specific order in the pipeline.
                # Essentially viewer < observer < subject < admin
                "$last" => "$access"
              },
              "reportId" => {
                "$last" => "$report._id"
              },
              "processId" => {
                "$first" => "$processId"
              },
              "subjectName" => {
                "$first" => "$subjectName"
              },
              "surveyToQuestions" => {
                "$first" => "$survey.survey_to_questions"
              }
            }
          },
          # We have this list of survey_ids, we need to join to the surveys collection. We only
          # want the ones in the account that are active or closed and are not deleted
          {
            "$lookup" => {
              "from" => "surveys",
              "as" => "survey",
              "localField" => "surveyId",
              "foreignField" => "_id",
              "pipeline" => [
                {
                  "$match" => {
                    "account_id" => account_id,
                    "status" => {
                      "$in" => [
                        :active, :closed
                      ]
                    },
                    "deleted_at" => nil
                  }
                }
              ]
            }
          },
          {
            "$unwind" => {
              "path" => "$survey"
            }
          }, {
            "$project" => {
              "surveyName" => "$survey.name",
              "type" => "$survey.type",
              "sortDate" => {"$ifNull" => ["$survey.closed_at", "$survey.created_at"]},
              "archived" => "$survey.archived",
              "status" => "$survey.status",
              "updatedAt" => "$survey.updated_at",
              "createdAt" => "$survey.created_at",
              "productType" => "$survey.product_type",
              "reportId" => {
                "$toString" => "$reportId"
              },
              "surveyId" => {
                "$toString" => "$survey._id"
              },
              "access" => {"$ifNull" => ["$access", "viewer"]},
              "subjectName" => "$subjectName",
              "surveyAggregateId" => "$survey.aggregate_id",
              "surveyToQuestions" => "$surveyToQuestions",
              "processId" => {"$toString" => "$processId"}
            }
          },
          {"$sort" => {"status" => 1, "sortDate" => -1, "surveyName" => 1, "subjectName" => -1}},
          {"$facet" => {
            "metaData" => [{"$count" => "total"}],
            "records" => [
              {"$project" => {
                "_id" => 0,
                "surveyId" => 1,
                "surveyName" => 1,
                "type" => 1,
                "archived" => 1,
                "status" => {
                  "$cond" => [
                    {
                      "$eq" => [true, "$archived"]
                    }, "archived", "$status"
                  ]
                },
                "updatedAt" => 1,
                "createdAt" => 1,
                "productType" => 1,
                "reportId" => 1,
                "access" => 1,
                "subjectName" => 1,
                "surveyAggregateId" => 1,
                "accountAggregateId" => account_aggregate_id,
                "surveyToQuestions" => 1,
                "processId" => 1
              }}
            ]
          }}
        ]
        raw_results = ReportAccessGrant.collection.aggregate(pipeline, read: {mode: :secondary})
        final_result = nil
        raw_results.each do |doc|
          reports = doc["records"] || []
          total = doc["metaData"]&.first ? doc["metaData"].first["total"] : 0
          final_result = {reports: reports, total: total}
        end
        final_result
      end
    end

    def all_reports_for_account
      @tracer.trace("all_reports_for_account") do
        user_id = @user.id
        account_id = @account.id
        account_aggregate_id = @account.aggregate_id
        result = Survey.collection.aggregate(
          [
            {"$match" => {"account_id" => account_id, "status" => {"$in" => [:active, :closed]}, "deleted_at" => nil}},
            {
              "$project" => {
                "name" => 1,
                "type" => 1,
                "sortDate" => {"$ifNull" => ["$survey.closed_at", "$survey.created_at"]},
                "archived" => 1,
                "status" => 1,
                "update_at" => 1,
                "created_at" => 1,
                "product_type" => 1,
                "aggregate_id" => 1
              }
            },
            # Get any survey_topics that the admin user is the subject, observer for.
            {
              "$unionWith" => {
                "coll" => "survey_topics",
                "pipeline" => [
                  {
                    "$match" => {
                      "$or" => [
                        {
                          "subject_id" => user_id,
                          "status" => {"$ne" => "deleted", "$in" => ["shared", "interviewed", "closed"]}
                        }, {
                          "observer_ids" => {"$in" => [user_id]},
                          "status" => {"$ne" => "deleted", "$in" => ["shared", "interviewed", "closed"]}
                        }
                        # Interviewer surveys that you have admin access to are skipped they will come as surveys in the main call
                      ],
                      "demonstration" => {"$ne" => true}
                    }
                  },
                  # Join the users collection to get the subject name
                  {
                    "$lookup" => {
                      "from" => "users",
                      "as" => "user",
                      "localField" => "subject_id",
                      "foreignField" => "_id"
                    }
                  }, {
                    "$unwind" => {
                      "path" => "$user"
                    }
                  }, {
                    "$project" => {
                      "surveyId" => "$survey_id",
                      "subjectName" => "$user.name",
                      "access" => {
                        "$mergeObjects" => [
                          # We treat subject/observer the same way and we want subject access to override observer access.
                          {
                            "$cond" => [
                              {
                                "$in" => [
                                  user_id, {
                                    "$ifNull" => [
                                      "$observer_ids", []
                                    ]
                                  }
                                ]
                              }, {
                                "access1" => "observer"
                              }, nil
                            ]
                          },
                          {
                            "$cond" => [
                              {
                                "$eq" => [
                                  "$subject_id", user_id
                                ]
                              }, {
                                "access1" => "subject"
                              }, nil
                            ]
                          }
                        ]
                      },
                      "processId" => "$_id"
                    }
                  }, {
                    "$project" => {
                      "access" => {
                        "$objectToArray" => "$access"
                      },
                      "subjectName" => 1,
                      "surveyId" => 1
                    }
                  }, {
                    "$unwind" => {
                      "path" => "$access"
                    }
                  },
                  {
                    "$project" => {
                      # Interviewer access doesn't need the processId
                      "processId" => 1,
                      "access" => "$access.v",
                      # Interviewer access doesn't need the subjectName
                      "subjectName" => 1,
                      "surveyId" => 1
                    }
                  },
                  {
                    "$lookup" => {
                      "from" => "surveys",
                      "localField" => "surveyId",
                      "foreignField" => "_id",
                      "as" => "survey",
                      "pipeline" => [
                        {"$match" => {"account_id" => account_id, "status" => {"$in" => [:active, :closed]}, "deleted_at" => nil, "type" => "three_sixty"}}
                      ]
                    }
                  },
                  {
                    "$unwind" => {
                      "path" => "$survey"
                    }
                  },
                  {"$project" => {
                    "_id" => "$survey._id",
                    "name" => "$survey.name",
                    "type" => "$survey.type",
                    "archived" => 1,
                    "subjectName" => 1,
                    "sortDate" => {"$ifNull" => ["$survey.closed_at", "$survey.created_at"]},
                    "status" => "$survey.status",
                    "updated_at" => "$survey.updated_at",
                    "created_at" => "$survey.created_at",
                    "access" => "$access",
                    "aggregate_id" => "$survey.aggregate_id"
                  }}
                ]
              }
            },
            {"$sort" => {"status" => 1, "sortDate" => -1, "surveyName" => 1, "subjectName" => -1}},
            {
              "$facet" => {
                "metaData" => [{"$count" => "total"}],
                "records" => [
                  {"$project" => {
                    "_id" => 0,
                    "surveyName" => "$name",
                    "type" => 1,
                    "status" => {
                      "$cond" => [
                        {
                          "$eq" => [true, "$archived"]
                        }, "archived", "$status"
                      ]
                    },
                    "subjectName" => 1,
                    "updatedAt" => "$updated_at",
                    "createdAt" => "$created_at",
                    "surveyId" => {"$toString" => "$_id"},
                    "access" => {"$ifNull" => ["$access", "admin"]},
                    "surveyAggregateId" => "$aggregate_id",
                    "accountAggregateId" => account_aggregate_id
                  }}
                ]
              }
            }
          ],
          read: {mode: :secondary}
        )
        final_result = nil
        result.each do |doc|
          reports = doc["records"] || []
          total = doc["metaData"]&.first ? doc["metaData"].first["total"] : 0
          final_result = {reports: reports, total: total}
        end
        final_result
      end
    end
  end
end
